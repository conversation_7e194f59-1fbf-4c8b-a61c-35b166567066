﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Essentials;

namespace Mraznicka.Models
{
    internal class Zvuk
    {
        public int enable { get; set; }
        private float hlasitost;

        public Zvuk()
        {
            enable = 1;
            hlasitost = 0.5f;
        }
        public float Hlasitost
        {
            get => hlasitost;
            set
            {
                hlasitost = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Hlasitost)));
            }
        }

        public async Task Speech(string strText)
        {
            if (strText == null)
                return;
            if (enable == 1)
            {
                await TextToSpeech.SpeakAsync(strText, new SpeechOptions
                {
                    Volume = hlasitost,
                }
                    );
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
    }
}
