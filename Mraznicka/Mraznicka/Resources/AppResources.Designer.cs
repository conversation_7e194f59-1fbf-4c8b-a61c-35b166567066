﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mraznicka.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class AppResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AppResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mraznicka.Resources.AppResources", typeof(AppResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About freezer.
        /// </summary>
        internal static string about {
            get {
                return ResourceManager.GetString("about", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to        who thinks for you.
        /// </summary>
        internal static string about_mysli {
            get {
                return ResourceManager.GetString("about_mysli", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After a simple initial connection, which you can handle yourself, your phone will allow you the following uses and benefits:.
        /// </summary>
        internal static string about_riadok_1 {
            get {
                return ResourceManager.GetString("about_riadok_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - Expiry date reminders..
        /// </summary>
        internal static string about_riadok_2 {
            get {
                return ResourceManager.GetString("about_riadok_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - Permanent overview of the contents of the freezer on your phone (anywhere and anytime)..
        /// </summary>
        internal static string about_riadok_3 {
            get {
                return ResourceManager.GetString("about_riadok_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - Makes shopping and cooking planning easier..
        /// </summary>
        internal static string about_riadok_4 {
            get {
                return ResourceManager.GetString("about_riadok_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - Quick search for food in the freezer..
        /// </summary>
        internal static string about_riadok_5 {
            get {
                return ResourceManager.GetString("about_riadok_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to - You can use all the functions, for example, in the refrigerator or when making preserves..
        /// </summary>
        internal static string about_riadok_6 {
            get {
                return ResourceManager.GetString("about_riadok_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string ano {
            get {
                return ResourceManager.GetString("ano", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pork.
        /// </summary>
        internal static string bravcove {
            get {
                return ResourceManager.GetString("bravcove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Herbs.
        /// </summary>
        internal static string bylinky {
            get {
                return ResourceManager.GetString("bylinky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clever freezer.
        /// </summary>
        internal static string chytra_mraznicka {
            get {
                return ResourceManager.GetString("chytra_mraznicka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sweets.
        /// </summary>
        internal static string cukrovinky {
            get {
                return ResourceManager.GetString("cukrovinky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Creation date.
        /// </summary>
        internal static string datumvytvorenia {
            get {
                return ResourceManager.GetString("datumvytvorenia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Day.
        /// </summary>
        internal static string den {
            get {
                return ResourceManager.GetString("den", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room details.
        /// </summary>
        internal static string detailmiestnosti {
            get {
                return ResourceManager.GetString("detailmiestnosti", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item details.
        /// </summary>
        internal static string detailpolozky {
            get {
                return ResourceManager.GetString("detailpolozky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position details.
        /// </summary>
        internal static string detailpozicie {
            get {
                return ResourceManager.GetString("detailpozicie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods details.
        /// </summary>
        internal static string detailtovaru {
            get {
                return ResourceManager.GetString("detailtovaru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detailed description of the device.
        /// </summary>
        internal static string detailzariadenia {
            get {
                return ResourceManager.GetString("detailzariadenia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Days.
        /// </summary>
        internal static string dni {
            get {
                return ResourceManager.GetString("dni", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Days.
        /// </summary>
        internal static string dní {
            get {
                return ResourceManager.GetString("dní", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Until expiration: .
        /// </summary>
        internal static string do_expiracie {
            get {
                return ResourceManager.GetString("do_expiracie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EAN.
        /// </summary>
        internal static string ean {
            get {
                return ResourceManager.GetString("ean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EAN is not used.
        /// </summary>
        internal static string eansanepouziva {
            get {
                return ResourceManager.GetString("eansanepouziva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Empty TAG.
        /// </summary>
        internal static string emptytag {
            get {
                return ResourceManager.GetString("emptytag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiration.
        /// </summary>
        internal static string expiracia {
            get {
                return ResourceManager.GetString("expiracia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiration (day).
        /// </summary>
        internal static string expiraciadni {
            get {
                return ResourceManager.GetString("expiraciadni", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realy set factory settings? All items they will be delete!.
        /// </summary>
        internal static string factory_setting_otazka_text {
            get {
                return ResourceManager.GetString("factory_setting_otazka_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory settings.
        /// </summary>
        internal static string factory_setting_otazka_title {
            get {
                return ResourceManager.GetString("factory_setting_otazka_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory settings.
        /// </summary>
        internal static string factory_settings {
            get {
                return ResourceManager.GetString("factory_settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory settings.
        /// </summary>
        internal static string factory_settings_title {
            get {
                return ResourceManager.GetString("factory_settings_title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        internal static string filter {
            get {
                return ResourceManager.GetString("filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to formatingtagoperationsucceessful.
        /// </summary>
        internal static string formatingtagoperationsucceessful {
            get {
                return ResourceManager.GetString("formatingtagoperationsucceessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wanted item.
        /// </summary>
        internal static string hladana_polozka {
            get {
                return ResourceManager.GetString("hladana_polozka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weigth (g).
        /// </summary>
        internal static string hmotnost {
            get {
                return ResourceManager.GetString("hmotnost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready meal.
        /// </summary>
        internal static string hotovejedlo {
            get {
                return ResourceManager.GetString("hotovejedlo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beef.
        /// </summary>
        internal static string hovadzie {
            get {
                return ResourceManager.GetString("hovadzie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Poultry.
        /// </summary>
        internal static string hydina {
            get {
                return ResourceManager.GetString("hydina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ikon.
        /// </summary>
        internal static string icon {
            get {
                return ResourceManager.GetString("icon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        internal static string ine {
            get {
                return ResourceManager.GetString("ine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information in Web.
        /// </summary>
        internal static string informacieweb {
            get {
                return ResourceManager.GetString("informacieweb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category.
        /// </summary>
        internal static string kategoria {
            get {
                return ResourceManager.GetString("kategoria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chicken.
        /// </summary>
        internal static string kuracie {
            get {
                return ResourceManager.GetString("kuracie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Licence number.
        /// </summary>
        internal static string licencnecislo {
            get {
                return ResourceManager.GetString("licencnecislo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual.
        /// </summary>
        internal static string manual {
            get {
                return ResourceManager.GetString("manual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual pull item.
        /// </summary>
        internal static string manualnyvyber {
            get {
                return ResourceManager.GetString("manualnyvyber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meat product.
        /// </summary>
        internal static string masovevyrobky {
            get {
                return ResourceManager.GetString("masovevyrobky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Milk production.
        /// </summary>
        internal static string mliecnevyrobky {
            get {
                return ResourceManager.GetString("mliecnevyrobky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Must by one item.
        /// </summary>
        internal static string musiexistovataspomjedenzaznam {
            get {
                return ResourceManager.GetString("musiexistovataspomjedenzaznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Found item.
        /// </summary>
        internal static string najdena_polozka {
            get {
                return ResourceManager.GetString("najdena_polozka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realy delete item?.
        /// </summary>
        internal static string naozajchcetevymazatzaznam {
            get {
                return ResourceManager.GetString("naozajchcetevymazatzaznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string nazov {
            get {
                return ResourceManager.GetString("nazov", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License key is not valid.
        /// </summary>
        internal static string neplatny_licencny_kluc {
            get {
                return ResourceManager.GetString("neplatny_licencny_kluc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG is not used.
        /// </summary>
        internal static string nepuzivany_tag {
            get {
                return ResourceManager.GetString("nepuzivany_tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EAN not equal.
        /// </summary>
        internal static string nespravny_ean {
            get {
                return ResourceManager.GetString("nespravny_ean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG not equal.
        /// </summary>
        internal static string nespravny_tag {
            get {
                return ResourceManager.GetString("nespravny_tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NFC is disable.
        /// </summary>
        internal static string nfcisdissabled {
            get {
                return ResourceManager.GetString("nfcisdissabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NFC is not available.
        /// </summary>
        internal static string nfcisnotavailable {
            get {
                return ResourceManager.GetString("nfcisnotavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string nie {
            get {
                return ResourceManager.GetString("nie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No possible delete item.
        /// </summary>
        internal static string niejemoznevymazatzaznam {
            get {
                return ResourceManager.GetString("niejemoznevymazatzaznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No TAG found.
        /// </summary>
        internal static string notagfound {
            get {
                return ResourceManager.GetString("notagfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New room.
        /// </summary>
        internal static string novamiestnost {
            get {
                return ResourceManager.GetString("novamiestnost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New item.
        /// </summary>
        internal static string novapolozka {
            get {
                return ResourceManager.GetString("novapolozka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New position.
        /// </summary>
        internal static string novapozicia {
            get {
                return ResourceManager.GetString("novapozicia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New device.
        /// </summary>
        internal static string novezariadenie {
            get {
                return ResourceManager.GetString("novezariadenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New goods item.
        /// </summary>
        internal static string novytovar {
            get {
                return ResourceManager.GetString("novytovar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About the freezer.
        /// </summary>
        internal static string o_mraznicke {
            get {
                return ResourceManager.GetString("o_mraznicke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ok.
        /// </summary>
        internal static string ok {
            get {
                return ResourceManager.GetString("ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fruit.
        /// </summary>
        internal static string ovocie {
            get {
                return ResourceManager.GetString("ovocie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to notification.
        /// </summary>
        internal static string oznam {
            get {
                return ResourceManager.GetString("oznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pastries.
        /// </summary>
        internal static string pecivo {
            get {
                return ResourceManager.GetString("pecivo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After expiration: .
        /// </summary>
        internal static string po_expiracii {
            get {
                return ResourceManager.GetString("po_expiracii", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Semi-finished product.
        /// </summary>
        internal static string polotovar {
            get {
                return ResourceManager.GetString("polotovar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The item has been deleted.
        /// </summary>
        internal static string polozka_bola_vymazana {
            get {
                return ResourceManager.GetString("polozka_bola_vymazana", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The EAN item has been deleted.
        /// </summary>
        internal static string polozka_ean_vymazana {
            get {
                return ResourceManager.GetString("polozka_ean_vymazana", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TAG item has been deleted.
        /// </summary>
        internal static string polozka_tag_vymazana {
            get {
                return ResourceManager.GetString("polozka_tag_vymazana", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string popis {
            get {
                return ResourceManager.GetString("popis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description position detail.
        /// </summary>
        internal static string popispoziciadetail {
            get {
                return ResourceManager.GetString("popispoziciadetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position list.
        /// </summary>
        internal static string popispozicialist {
            get {
                return ResourceManager.GetString("popispozicialist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description position creation.
        /// </summary>
        internal static string popispoziciavytvorenie {
            get {
                return ResourceManager.GetString("popispoziciavytvorenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description goods detail.
        /// </summary>
        internal static string popistovardetail {
            get {
                return ResourceManager.GetString("popistovardetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description goods creation.
        /// </summary>
        internal static string popistovarvytvorenie {
            get {
                return ResourceManager.GetString("popistovarvytvorenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description goods list.
        /// </summary>
        internal static string popistovarzoznam {
            get {
                return ResourceManager.GetString("popistovarzoznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to popisvlozenie.
        /// </summary>
        internal static string popisvlozenie {
            get {
                return ResourceManager.GetString("popisvlozenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description search.
        /// </summary>
        internal static string popisvyber {
            get {
                return ResourceManager.GetString("popisvyber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List device record detail.
        /// </summary>
        internal static string popiszariadeniedetail {
            get {
                return ResourceManager.GetString("popiszariadeniedetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create device record.
        /// </summary>
        internal static string popiszariadenievytvorenie {
            get {
                return ResourceManager.GetString("popiszariadenievytvorenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List device record.
        /// </summary>
        internal static string popiszariadeniezoznam {
            get {
                return ResourceManager.GetString("popiszariadeniezoznam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last used.
        /// </summary>
        internal static string poslednepouzite {
            get {
                return ResourceManager.GetString("poslednepouzite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position.
        /// </summary>
        internal static string pozicia {
            get {
                return ResourceManager.GetString("pozicia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note.
        /// </summary>
        internal static string poznamka {
            get {
                return ResourceManager.GetString("poznamka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        internal static string prehlad {
            get {
                return ResourceManager.GetString("prehlad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View with choice.
        /// </summary>
        internal static string prehlad_main {
            get {
                return ResourceManager.GetString("prehlad_main", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string pridat {
            get {
                return ResourceManager.GetString("pridat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record can not by null.
        /// </summary>
        internal static string recordcantbenull {
            get {
                return ResourceManager.GetString("recordcantbenull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration successfull.
        /// </summary>
        internal static string registracia_prebehla_uspesne {
            get {
                return ResourceManager.GetString("registracia_prebehla_uspesne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restore.
        /// </summary>
        internal static string restore {
            get {
                return ResourceManager.GetString("restore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fish.
        /// </summary>
        internal static string ryby {
            get {
                return ResourceManager.GetString("ryby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EAN equal.
        /// </summary>
        internal static string spravny_ean {
            get {
                return ResourceManager.GetString("spravny_ean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG equal.
        /// </summary>
        internal static string spravny_tag {
            get {
                return ResourceManager.GetString("spravny_tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position.
        /// </summary>
        internal static string suflik {
            get {
                return ResourceManager.GetString("suflik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG.
        /// </summary>
        internal static string tag {
            get {
                return ResourceManager.GetString("tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tag is used.
        /// </summary>
        internal static string tag_sa_uz_pouziva {
            get {
                return ResourceManager.GetString("tag_sa_uz_pouziva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG ID.
        /// </summary>
        internal static string tagid {
            get {
                return ResourceManager.GetString("tagid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG Info.
        /// </summary>
        internal static string taginfo {
            get {
                return ResourceManager.GetString("taginfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TAG not used.
        /// </summary>
        internal static string tagsanepouziva {
            get {
                return ResourceManager.GetString("tagsanepouziva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attach the phone to EAN.
        /// </summary>
        internal static string telefon_ku_ean {
            get {
                return ResourceManager.GetString("telefon_ku_ean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attach the phone to TAG.
        /// </summary>
        internal static string telefon_ku_tag {
            get {
                return ResourceManager.GetString("telefon_ku_tag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to the goods.
        /// </summary>
        internal static string tovar {
            get {
                return ResourceManager.GetString("tovar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string ulozit {
            get {
                return ResourceManager.GetString("ulozit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsuported TAG.
        /// </summary>
        internal static string unsupportedtag {
            get {
                return ResourceManager.GetString("unsupportedtag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User cancel sesion.
        /// </summary>
        internal static string userhascancelnfcsession {
            get {
                return ResourceManager.GetString("userhascancelnfcsession", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Introduction.
        /// </summary>
        internal static string uvod {
            get {
                return ResourceManager.GetString("uvod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home page.
        /// </summary>
        internal static string uvodnastranka {
            get {
                return ResourceManager.GetString("uvodnastranka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This application will allow you to check what, where and how long you have stored in the freezer..
        /// </summary>
        internal static string uvodnastranka_text {
            get {
                return ResourceManager.GetString("uvodnastranka_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version .
        /// </summary>
        internal static string verzia {
            get {
                return ResourceManager.GetString("verzia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1.5.7.
        /// </summary>
        internal static string verzia_cislo {
            get {
                return ResourceManager.GetString("verzia_cislo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stored:.
        /// </summary>
        internal static string vlozene {
            get {
                return ResourceManager.GetString("vlozene", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push.
        /// </summary>
        internal static string vlozenie {
            get {
                return ResourceManager.GetString("vlozenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The insertion was successful.
        /// </summary>
        internal static string vlozenie_prebehlo_uspesne {
            get {
                return ResourceManager.GetString("vlozenie_prebehlo_uspesne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push manually.
        /// </summary>
        internal static string vlozeniemanualne {
            get {
                return ResourceManager.GetString("vlozeniemanualne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push by EAN.
        /// </summary>
        internal static string vlozeniepomocouean {
            get {
                return ResourceManager.GetString("vlozeniepomocouean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push by TAG.
        /// </summary>
        internal static string vlozeniepomocoutag {
            get {
                return ResourceManager.GetString("vlozeniepomocoutag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pull.
        /// </summary>
        internal static string vyber {
            get {
                return ResourceManager.GetString("vyber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pull.
        /// </summary>
        internal static string vyber_main {
            get {
                return ResourceManager.GetString("vyber_main", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pull by EAN.
        /// </summary>
        internal static string vyberpomocouean {
            get {
                return ResourceManager.GetString("vyberpomocouean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pull by TAG.
        /// </summary>
        internal static string vyberpomocoutag {
            get {
                return ResourceManager.GetString("vyberpomocoutag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select goods - all.
        /// </summary>
        internal static string vyberte_tovar_vsetko {
            get {
                return ResourceManager.GetString("vyberte_tovar_vsetko", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select device - all .
        /// </summary>
        internal static string vyberte_zariadenia_vsetky {
            get {
                return ResourceManager.GetString("vyberte_zariadenia_vsetky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete record.
        /// </summary>
        internal static string vymazaniezaznamu {
            get {
                return ResourceManager.GetString("vymazaniezaznamu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Writing tag is not supported on this device.
        /// </summary>
        internal static string writingtagisnotsupportedonthisdevice {
            get {
                return ResourceManager.GetString("writingtagisnotsupportedonthisdevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Writing tag operation successful.
        /// </summary>
        internal static string writingtagoperationsuccessful {
            get {
                return ResourceManager.GetString("writingtagoperationsuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter section of text mandatory.
        /// </summary>
        internal static string zadajte_opis_popisu {
            get {
                return ResourceManager.GetString("zadajte_opis_popisu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a description.
        /// </summary>
        internal static string zadajte_popis {
            get {
                return ResourceManager.GetString("zadajte_popis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a note.
        /// </summary>
        internal static string zadajte_poznamku {
            get {
                return ResourceManager.GetString("zadajte_poznamku", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter  Date Of Expiration.
        /// </summary>
        internal static string zadajteexpiraciu {
            get {
                return ResourceManager.GetString("zadajteexpiraciu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter weight.
        /// </summary>
        internal static string zadajtehmotnost {
            get {
                return ResourceManager.GetString("zadajtehmotnost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter licence number.
        /// </summary>
        internal static string zadajtelicencnecislo {
            get {
                return ResourceManager.GetString("zadajtelicencnecislo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter name.
        /// </summary>
        internal static string zadajtenazov {
            get {
                return ResourceManager.GetString("zadajtenazov", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This information is mandatory.
        /// </summary>
        internal static string zadajtepopis {
            get {
                return ResourceManager.GetString("zadajtepopis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter note.
        /// </summary>
        internal static string zadajtepoznamku {
            get {
                return ResourceManager.GetString("zadajtepoznamku", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Writing to TAG.
        /// </summary>
        internal static string zapisnatag {
            get {
                return ResourceManager.GetString("zapisnatag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device.
        /// </summary>
        internal static string zariadenie {
            get {
                return ResourceManager.GetString("zariadenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device not exist.
        /// </summary>
        internal static string zaznamneexistuje {
            get {
                return ResourceManager.GetString("zaznamneexistuje", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to vegetables.
        /// </summary>
        internal static string zelenina {
            get {
                return ResourceManager.GetString("zelenina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string zmazat {
            get {
                return ResourceManager.GetString("zmazat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rooms list.
        /// </summary>
        internal static string zoznammiestnosti {
            get {
                return ResourceManager.GetString("zoznammiestnosti", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item list.
        /// </summary>
        internal static string zoznampoloziek {
            get {
                return ResourceManager.GetString("zoznampoloziek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Positons list.
        /// </summary>
        internal static string zoznampozicii {
            get {
                return ResourceManager.GetString("zoznampozicii", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category list.
        /// </summary>
        internal static string zoznamtovarov {
            get {
                return ResourceManager.GetString("zoznamtovarov", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device list.
        /// </summary>
        internal static string zoznamzariadeni {
            get {
                return ResourceManager.GetString("zoznamzariadeni", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string zrusit {
            get {
                return ResourceManager.GetString("zrusit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Venison.
        /// </summary>
        internal static string zverina {
            get {
                return ResourceManager.GetString("zverina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Good.
        /// </summary>
        internal static string zvuk_dobre {
            get {
                return ResourceManager.GetString("zvuk_dobre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar code not used.
        /// </summary>
        internal static string zvuk_ean_sa_nepouziva {
            get {
                return ResourceManager.GetString("zvuk_ean_sa_nepouziva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control to expiration.
        /// </summary>
        internal static string zvuk_kontrola_expiracie {
            get {
                return ResourceManager.GetString("zvuk_kontrola_expiracie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beep.
        /// </summary>
        internal static string zvuk_pip {
            get {
                return ResourceManager.GetString("zvuk_pip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tag not used.
        /// </summary>
        internal static string zvuk_tag_sa_nepouziva {
            get {
                return ResourceManager.GetString("zvuk_tag_sa_nepouziva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bad.
        /// </summary>
        internal static string zvuk_zle {
            get {
                return ResourceManager.GetString("zvuk_zle", resourceCulture);
            }
        }
    }
}
