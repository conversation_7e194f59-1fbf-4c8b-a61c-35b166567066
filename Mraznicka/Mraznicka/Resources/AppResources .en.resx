﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="about" xml:space="preserve">
    <value>About freezer</value>
  </data>
  <data name="about_mysli" xml:space="preserve">
    <value>       who thinks for you</value>
  </data>
  <data name="about_riadok_1" xml:space="preserve">
    <value>After a simple initial connection, which you can handle yourself, your phone will allow you the following uses and benefits:</value>
  </data>
  <data name="about_riadok_2" xml:space="preserve">
    <value>- Expiry date reminders.</value>
  </data>
  <data name="about_riadok_3" xml:space="preserve">
    <value>- Permanent overview of the contents of the freezer on your phone (anywhere and anytime).</value>
  </data>
  <data name="about_riadok_4" xml:space="preserve">
    <value>- Makes shopping and cooking planning easier.</value>
  </data>
  <data name="about_riadok_5" xml:space="preserve">
    <value>- Quick search for food in the freezer.</value>
  </data>
  <data name="about_riadok_6" xml:space="preserve">
    <value>- You can use all the functions, for example, in the refrigerator or when making preserves.</value>
  </data>
  <data name="ano" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="bravcove" xml:space="preserve">
    <value>Pork</value>
  </data>
  <data name="bylinky" xml:space="preserve">
    <value>Herbs</value>
  </data>
  <data name="chytra_mraznicka" xml:space="preserve">
    <value>Clever freezer</value>
  </data>
  <data name="cukrovinky" xml:space="preserve">
    <value>Sweets</value>
  </data>
  <data name="datumvytvorenia" xml:space="preserve">
    <value>Creation date</value>
  </data>
  <data name="den" xml:space="preserve">
    <value> Day</value>
  </data>
  <data name="detailmiestnosti" xml:space="preserve">
    <value>Room details</value>
  </data>
  <data name="detailpolozky" xml:space="preserve">
    <value>Item details</value>
  </data>
  <data name="detailpozicie" xml:space="preserve">
    <value>Position details</value>
  </data>
  <data name="detailtovaru" xml:space="preserve">
    <value>Goods details</value>
  </data>
  <data name="detailzariadenia" xml:space="preserve">
    <value>Detailed description of the device</value>
  </data>
  <data name="dni" xml:space="preserve">
    <value> Days</value>
  </data>
  <data name="dní" xml:space="preserve">
    <value> Days</value>
  </data>
  <data name="do_expiracie" xml:space="preserve">
    <value>Until expiration: </value>
  </data>
  <data name="ean" xml:space="preserve">
    <value>EAN</value>
  </data>
  <data name="eansanepouziva" xml:space="preserve">
    <value>EAN is not used</value>
  </data>
  <data name="emptytag" xml:space="preserve">
    <value>Empty TAG</value>
  </data>
  <data name="expiracia" xml:space="preserve">
    <value>Expiration</value>
  </data>
  <data name="expiraciadni" xml:space="preserve">
    <value>Expiration (day)</value>
  </data>
  <data name="factory_settings" xml:space="preserve">
    <value>Factory settings</value>
  </data>
  <data name="factory_settings_title" xml:space="preserve">
    <value>Factory settings</value>
  </data>
  <data name="factory_setting_otazka_text" xml:space="preserve">
    <value>Realy set factory settings? All items they will be delete!</value>
  </data>
  <data name="factory_setting_otazka_title" xml:space="preserve">
    <value>Factory settings</value>
  </data>
  <data name="filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="formatingtagoperationsucceessful" xml:space="preserve">
    <value>formatingtagoperationsucceessful</value>
  </data>
  <data name="hladana_polozka" xml:space="preserve">
    <value>Wanted item</value>
  </data>
  <data name="hmotnost" xml:space="preserve">
    <value>Weigth (g)</value>
  </data>
  <data name="hotovejedlo" xml:space="preserve">
    <value>Ready meal</value>
  </data>
  <data name="hovadzie" xml:space="preserve">
    <value>Beef</value>
  </data>
  <data name="hydina" xml:space="preserve">
    <value>Poultry</value>
  </data>
  <data name="icon" xml:space="preserve">
    <value>Ikon</value>
  </data>
  <data name="ine" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="informacieweb" xml:space="preserve">
    <value>Information in Web</value>
  </data>
  <data name="kategoria" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="kuracie" xml:space="preserve">
    <value>Chicken</value>
  </data>
  <data name="licencnecislo" xml:space="preserve">
    <value>Licence number</value>
  </data>
  <data name="manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="manualnyvyber" xml:space="preserve">
    <value>Manual pull item</value>
  </data>
  <data name="masovevyrobky" xml:space="preserve">
    <value>Meat product</value>
  </data>
  <data name="mliecnevyrobky" xml:space="preserve">
    <value>Milk production</value>
  </data>
  <data name="musiexistovataspomjedenzaznam" xml:space="preserve">
    <value>Must by one item</value>
  </data>
  <data name="najdena_polozka" xml:space="preserve">
    <value>Found item</value>
  </data>
  <data name="naozajchcetevymazatzaznam" xml:space="preserve">
    <value>Realy delete item?</value>
  </data>
  <data name="nazov" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="neplatny_licencny_kluc" xml:space="preserve">
    <value>License key is not valid</value>
  </data>
  <data name="nepuzivany_tag" xml:space="preserve">
    <value>TAG is not used</value>
  </data>
  <data name="nespravny_ean" xml:space="preserve">
    <value>EAN not equal</value>
  </data>
  <data name="nespravny_tag" xml:space="preserve">
    <value>TAG not equal</value>
  </data>
  <data name="nfcisdissabled" xml:space="preserve">
    <value>NFC is disable</value>
  </data>
  <data name="nfcisnotavailable" xml:space="preserve">
    <value>NFC is not available</value>
  </data>
  <data name="nie" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="niejemoznevymazatzaznam" xml:space="preserve">
    <value>No possible delete item</value>
  </data>
  <data name="notagfound" xml:space="preserve">
    <value>No TAG found</value>
  </data>
  <data name="novamiestnost" xml:space="preserve">
    <value>New room</value>
  </data>
  <data name="novapolozka" xml:space="preserve">
    <value>New item</value>
  </data>
  <data name="novapozicia" xml:space="preserve">
    <value>New position</value>
  </data>
  <data name="novezariadenie" xml:space="preserve">
    <value>New device</value>
  </data>
  <data name="novytovar" xml:space="preserve">
    <value>New goods item</value>
  </data>
  <data name="ok" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="ovocie" xml:space="preserve">
    <value>Fruit</value>
  </data>
  <data name="oznam" xml:space="preserve">
    <value>notification</value>
  </data>
  <data name="o_mraznicke" xml:space="preserve">
    <value>About the freezer</value>
  </data>
  <data name="pecivo" xml:space="preserve">
    <value>Pastries</value>
  </data>
  <data name="polotovar" xml:space="preserve">
    <value>Semi-finished product</value>
  </data>
  <data name="polozka_bola_vymazana" xml:space="preserve">
    <value>The item has been deleted</value>
  </data>
  <data name="polozka_ean_vymazana" xml:space="preserve">
    <value>The EAN item has been deleted</value>
  </data>
  <data name="polozka_tag_vymazana" xml:space="preserve">
    <value>The TAG item has been deleted</value>
  </data>
  <data name="popis" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="popispoziciadetail" xml:space="preserve">
    <value>Description position detail</value>
  </data>
  <data name="popispozicialist" xml:space="preserve">
    <value>Position list</value>
  </data>
  <data name="popispoziciavytvorenie" xml:space="preserve">
    <value>Description position creation</value>
  </data>
  <data name="popistovardetail" xml:space="preserve">
    <value>Description goods detail</value>
  </data>
  <data name="popistovarvytvorenie" xml:space="preserve">
    <value>Description goods creation</value>
  </data>
  <data name="popistovarzoznam" xml:space="preserve">
    <value>Description goods list</value>
  </data>
  <data name="popisvlozenie" xml:space="preserve">
    <value>popisvlozenie</value>
  </data>
  <data name="popisvyber" xml:space="preserve">
    <value>Description search</value>
  </data>
  <data name="popiszariadeniedetail" xml:space="preserve">
    <value>List device record detail</value>
  </data>
  <data name="popiszariadenievytvorenie" xml:space="preserve">
    <value>Create device record</value>
  </data>
  <data name="popiszariadeniezoznam" xml:space="preserve">
    <value>List device record</value>
  </data>
  <data name="poslednepouzite" xml:space="preserve">
    <value>Last used</value>
  </data>
  <data name="pozicia" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="poznamka" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="po_expiracii" xml:space="preserve">
    <value>After expiration: </value>
  </data>
  <data name="prehlad" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="prehlad_main" xml:space="preserve">
    <value>View with choice</value>
  </data>
  <data name="pridat" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="recordcantbenull" xml:space="preserve">
    <value>Record can not by null</value>
  </data>
  <data name="registracia_prebehla_uspesne" xml:space="preserve">
    <value>Registration successfull</value>
  </data>
  <data name="restore" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="ryby" xml:space="preserve">
    <value>Fish</value>
  </data>
  <data name="spravny_ean" xml:space="preserve">
    <value>EAN equal</value>
  </data>
  <data name="spravny_tag" xml:space="preserve">
    <value>TAG equal</value>
  </data>
  <data name="suflik" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="tag" xml:space="preserve">
    <value>TAG</value>
  </data>
  <data name="tagid" xml:space="preserve">
    <value>TAG ID</value>
  </data>
  <data name="taginfo" xml:space="preserve">
    <value>TAG Info</value>
  </data>
  <data name="tagsanepouziva" xml:space="preserve">
    <value>TAG not used</value>
  </data>
  <data name="tag_sa_uz_pouziva" xml:space="preserve">
    <value>Tag is used</value>
  </data>
  <data name="telefon_ku_ean" xml:space="preserve">
    <value>Attach the phone to EAN</value>
  </data>
  <data name="telefon_ku_tag" xml:space="preserve">
    <value>Attach the phone to TAG</value>
  </data>
  <data name="tovar" xml:space="preserve">
    <value>the goods</value>
  </data>
  <data name="ulozit" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="unsupportedtag" xml:space="preserve">
    <value>Unsuported TAG</value>
  </data>
  <data name="userhascancelnfcsession" xml:space="preserve">
    <value>User cancel sesion</value>
  </data>
  <data name="uvod" xml:space="preserve">
    <value>Introduction</value>
  </data>
  <data name="uvodnastranka" xml:space="preserve">
    <value>Home page</value>
  </data>
  <data name="uvodnastranka_text" xml:space="preserve">
    <value>This application will allow you to check what, where and how long you have stored in the freezer.</value>
  </data>
  <data name="verzia" xml:space="preserve">
    <value>Version </value>
  </data>
  <data name="verzia_cislo_en" xml:space="preserve">
    <value>toto nechceme meniť v lokalizacii</value>
  </data>
  <data name="vlozene" xml:space="preserve">
    <value>Stored:</value>
  </data>
  <data name="vlozenie" xml:space="preserve">
    <value>Push</value>
  </data>
  <data name="vlozeniemanualne" xml:space="preserve">
    <value>Push manually</value>
  </data>
  <data name="vlozeniepomocouean" xml:space="preserve">
    <value>Push by EAN</value>
  </data>
  <data name="vlozeniepomocoutag" xml:space="preserve">
    <value>Push by TAG</value>
  </data>
  <data name="vlozenie_prebehlo_uspesne" xml:space="preserve">
    <value>The insertion was successful</value>
  </data>
  <data name="vyber" xml:space="preserve">
    <value>Pull</value>
  </data>
  <data name="vyberpomocouean" xml:space="preserve">
    <value>Pull by EAN</value>
  </data>
  <data name="vyberpomocoutag" xml:space="preserve">
    <value>Pull by TAG</value>
  </data>
  <data name="vyberte_tovar_vsetko" xml:space="preserve">
    <value>Select goods - all</value>
  </data>
  <data name="vyberte_zariadenia_vsetky" xml:space="preserve">
    <value>Select device - all </value>
  </data>
  <data name="vyber_main" xml:space="preserve">
    <value>Pull</value>
  </data>
  <data name="vymazaniezaznamu" xml:space="preserve">
    <value>Delete record</value>
  </data>
  <data name="writingtagisnotsupportedonthisdevice" xml:space="preserve">
    <value>Writing tag is not supported on this device</value>
  </data>
  <data name="writingtagoperationsuccessful" xml:space="preserve">
    <value>Writing tag operation successful</value>
  </data>
  <data name="zadajteexpiraciu" xml:space="preserve">
    <value>Enter  Date Of Expiration</value>
  </data>
  <data name="zadajtehmotnost" xml:space="preserve">
    <value>Enter weight</value>
  </data>
  <data name="zadajtelicencnecislo" xml:space="preserve">
    <value>Enter licence number</value>
  </data>
  <data name="zadajtenazov" xml:space="preserve">
    <value>Enter name</value>
  </data>
  <data name="zadajtepopis" xml:space="preserve">
    <value>This information is mandatory</value>
  </data>
  <data name="zadajtepoznamku" xml:space="preserve">
    <value>Enter note</value>
  </data>
  <data name="zadajte_opis_popisu" xml:space="preserve">
    <value>Enter section of text mandatory</value>
  </data>
  <data name="zadajte_popis" xml:space="preserve">
    <value>Enter a description</value>
  </data>
  <data name="zadajte_poznamku" xml:space="preserve">
    <value>Enter a note</value>
  </data>
  <data name="zapisnatag" xml:space="preserve">
    <value>Writing to TAG</value>
  </data>
  <data name="zariadenie" xml:space="preserve">
    <value>Device</value>
  </data>
  <data name="zaznamneexistuje" xml:space="preserve">
    <value>Device not exist</value>
  </data>
  <data name="zelenina" xml:space="preserve">
    <value>vegetables</value>
  </data>
  <data name="zmazat" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="zoznammiestnosti" xml:space="preserve">
    <value>Rooms list</value>
  </data>
  <data name="zoznampoloziek" xml:space="preserve">
    <value>Item list</value>
  </data>
  <data name="zoznampozicii" xml:space="preserve">
    <value>Positons list</value>
  </data>
  <data name="zoznamtovarov" xml:space="preserve">
    <value>Category list</value>
  </data>
  <data name="zoznamzariadeni" xml:space="preserve">
    <value>Device list</value>
  </data>
  <data name="zrusit" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="zverina" xml:space="preserve">
    <value>Venison</value>
  </data>
</root>