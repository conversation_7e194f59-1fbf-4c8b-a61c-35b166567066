﻿<?xml version="1.0" encoding="UTF-8"?>
<Shell xmlns="http://xamarin.com/schemas/2014/forms" 
       xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
       xmlns:views="clr-namespace:Mraznicka.Views"
	   xmlns:miestnost="clr-namespace:Mraznicka.Views.Miestnost"
	   xmlns:pozicia="clr-namespace:Mraznicka.Views.Pozicia"
	   xmlns:tovar="clr-namespace:Mraznicka.Views.Tovar"
	   xmlns:zariadenie="clr-namespace:Mraznicka.Views.Zariadenie"
	   xmlns:polozka="clr-namespace:Mraznicka.Views.Polozka"
	   xmlns:pages="clr-namespace:Mraznicka"
	   xmlns:sys="clr-namespace:System;assembly=netstandard"
       xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
       
       Title="Chytrá mraznič<PERSON>"
       x:Class="Mraznicka.AppShell">

	<!--
        The overall app visual hierarchy is defined here, along with navigation.
    
        https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/
    -->

    <Shell.Resources>
		<ResourceDictionary>
			<Style x:Key="BaseStyle" TargetType="Element">
				<Setter Property="Shell.BackgroundColor" Value="{StaticResource Primary}" />
				<Setter Property="Shell.ForegroundColor" Value="White" />
				<Setter Property="Shell.TitleColor" Value="White" />
				<Setter Property="Shell.DisabledColor" Value="#B4FFFFFF" />
				<Setter Property="Shell.UnselectedColor" Value="#95FFFFFF" />
				<Setter Property="Shell.TabBarBackgroundColor" Value="{StaticResource Primary}" />
				<Setter Property="Shell.TabBarForegroundColor" Value="White"/>
				<Setter Property="Shell.TabBarUnselectedColor" Value="#95FFFFFF"/>
				<Setter Property="Shell.TabBarTitleColor" Value="White"/>
			</Style>
			<Style TargetType="TabBar" BasedOn="{StaticResource BaseStyle}" />
			<Style TargetType="FlyoutItem" BasedOn="{StaticResource BaseStyle}" />

			<!--
            Default Styles for all Flyout Items
            https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#flyoutitem-and-menuitem-style-classes
            -->
			<Style Class="FlyoutItemLabelStyle" TargetType="Label">
				<Setter Property="TextColor" Value="White"></Setter>
			</Style>
			<Style Class="FlyoutItemLayoutStyle" TargetType="Layout" ApplyToDerivedTypes="True">
				<Setter Property="VisualStateManager.VisualStateGroups">
					<VisualStateGroupList>
						<VisualStateGroup x:Name="CommonStates">
							<VisualState x:Name="Normal">
								<VisualState.Setters>
									<Setter Property="BackgroundColor" Value="{x:OnPlatform UWP=Transparent, iOS=White}" />
									<Setter TargetName="FlyoutItemLabel" Property="Label.TextColor" Value="{StaticResource Primary}" />
								</VisualState.Setters>
							</VisualState>
							<VisualState x:Name="Selected">
								<VisualState.Setters>
									<Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
								</VisualState.Setters>
							</VisualState>
						</VisualStateGroup>
					</VisualStateGroupList>
				</Setter>
			</Style>

			<!--
            Custom Style you can apply to any Flyout Item
            -->
			<Style Class="MenuItemLayoutStyle" TargetType="Layout" ApplyToDerivedTypes="True">
				<Setter Property="VisualStateManager.VisualStateGroups">
					<VisualStateGroupList>
						<VisualStateGroup x:Name="CommonStates">
							<VisualState x:Name="Normal">
								<VisualState.Setters>
									<Setter TargetName="FlyoutItemLabel" Property="Label.TextColor" Value="{StaticResource Primary}" />
								</VisualState.Setters>
							</VisualState>
						</VisualStateGroup>
					</VisualStateGroupList>
				</Setter>
			</Style>
		</ResourceDictionary>
	</Shell.Resources>

	<!-- 
        When the Flyout is visible this defines the content to display in the flyout.
        FlyoutDisplayOptions="AsMultipleItems" will create a separate flyout item for each child element    
        https://docs.microsoft.com/dotnet/api/xamarin.forms.shellgroupitem.flyoutdisplayoptions?view=xamarin-forms
    -->
	
	<!--
	
	
	<FlyoutItem Title="O mrazničke" Icon="icon_about.png">
		<ShellContent Route="AboutPage" ContentTemplate="{DataTemplate views:AboutPage}" />
	</FlyoutItem>
	<FlyoutItem Title="Browse" Icon="icon_feed.png">
		<ShellContent Route="ItemsPage" ContentTemplate="{DataTemplate views:ItemsPage}" />
	</FlyoutItem>
	

	
	<FlyoutItem Title="Vyber" Icon="icon_feed.png">
		<ShellContent Route="ItemsPage" ContentTemplate="{DataTemplate views:ItemsPage}" />
	</FlyoutItem>
	<FlyoutItem Title="MainPage" Icon="icon_feed.png">
		<ShellContent ContentTemplate="{DataTemplate pages:MainPage}" />
	</FlyoutItem>

	<FlyoutItem Title="Ciselniky" Icon="icon_feed.png">
		<ShellContent ContentTemplate="{DataTemplate pages:CiselnikyVyber}" />
	</FlyoutItem>
-->

	<!--  https://social.msdn.microsoft.com/Forums/en-US/cf8b976c-a74e-47ce-9d22-079763858ca5/xamarin-forms-shell-separator-between-flyout-items?forum=xamarinforms  -->

	<!--<FlyoutItem Title="MainPage" Icon="icon_feed.png">
		<ShellContent ContentTemplate="{DataTemplate pages:MainPage}" />
	</FlyoutItem>-->

    <FlyoutItem Title="{i18n:Translate uvod}" Icon="icon_item.png">
		<ShellContent ContentTemplate="{DataTemplate views:MainPage}" />
	</FlyoutItem>

    <FlyoutItem Title="{i18n:Translate o_mraznicke}" Icon="icon_item.png">
		<ShellContent ContentTemplate="{DataTemplate views:AboutPage}" />
	</FlyoutItem>

	<!--FlyoutItem Title="Vyber" Icon="icon_pull.png">
		<ShellContent ContentTemplate="{DataTemplate pages:CiselnikyVyber}" />
	</FlyoutItem>
	<FlyoutItem Title="Vloz" Icon="icon_push.png">
		<ShellContent ContentTemplate="{DataTemplate miestnost:CreatePage}" />
	</FlyoutItem-->
	
	<!--<FlyoutItem Shell.TabBarIsVisible="False" FlyoutDisplayOptions="AsMultipleItems">
		<ShellContent Title="Číselníky" ContentTemplate="{DataTemplate pages:CiselnikyVyber}"/>
	</FlyoutItem>-->


	<!--<FlyoutItem Title="Miestnost" Icon="icon_locality.png">
		<ShellContent ContentTemplate="{DataTemplate pages:MiestnostCiselnik}" />
	</FlyoutItem>-->
	
	<!--<FlyoutItem Title="Miestnost" Icon="icon_locality.png">
		<ShellContent ContentTemplate="{DataTemplate miestnost:ListPage}" />
	</FlyoutItem>-->
    <FlyoutItem Title="{i18n:Translate pozicia}" Icon="icon_position.png">
		<ShellContent ContentTemplate="{DataTemplate pozicia:ListPage}" />
	</FlyoutItem>
    <FlyoutItem Title="{i18n:Translate kategoria}" Icon="icon_comodity.png">
		<ShellContent ContentTemplate="{DataTemplate tovar:ListPage}" />
	</FlyoutItem>
    <FlyoutItem Title="{i18n:Translate zariadenie}" Icon="icon_device.png">
		<ShellContent ContentTemplate="{DataTemplate zariadenie:ListPage}" />
	</FlyoutItem>
    <FlyoutItem Title="{i18n:Translate factory_settings}" Icon="icon_device.png">
        <ShellContent ContentTemplate="{DataTemplate views:FactoryPage}" />
    </FlyoutItem>
    <!--<FlyoutItem Title="Položka" Icon="icon_item.png">
		<ShellContent ContentTemplate="{DataTemplate polozka:ListPage}" />
	</FlyoutItem>-->

	<!--<FlyoutItem Title="Položka old" Icon="icon_item.png">
		<ShellContent ContentTemplate="{DataTemplate pages:PolozkaCiselnik}" />
	</FlyoutItem>-->

	<!--FlyoutItem Shell.TabBarIsVisible="False" FlyoutDisplayOptions="AsMultipleItems">
		<ShellContent Title="MainPage" ContentTemplate="{DataTemplate pages:MainPage}"/>
	</FlyoutItem-->

	<!-- When the Flyout is visible this will be a menu item you can tie a click behavior to  -->
	<!--<MenuItem Text="Logout" StyleClass="MenuItemLayoutStyle" Clicked="OnMenuItemClicked">
	</MenuItem>-->

	<!--
        TabBar lets you define content that won't show up in a flyout menu. When this content is active
        the flyout menu won't be available. This is useful for creating areas of the application where 
        you don't want users to be able to navigate away from. If you would like to navigate to this 
        content you can do so by calling 
        await Shell.Current.GoToAsync("//LoginPage");
    -->
	<!--<TabBar>
		<ShellContent Route="LoginPage" ContentTemplate="{DataTemplate views:LoginPage}" />
	</TabBar>-->

	<!-- Optional Templates 
    // These may be provided inline as below or as separate classes.

    // This header appears at the top of the Flyout.
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#flyout-header
    <Shell.FlyoutHeaderTemplate>
        <DataTemplate>
            <Grid>ContentHere</Grid>
        </DataTemplate>
    </Shell.FlyoutHeaderTemplate>

    // ItemTemplate is for ShellItems as displayed in a Flyout
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#define-flyoutitem-appearance
    <Shell.ItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Title, Icon
            </ContentView>
        </DataTemplate>
    </Shell.ItemTemplate>

    // MenuItemTemplate is for MenuItems as displayed in a Flyout
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#define-menuitem-appearance
    <Shell.MenuItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Text, Icon
            </ContentView>
        </DataTemplate>
    </Shell.MenuItemTemplate>

    -->
	<Shell.FlyoutHeaderTemplate>
		<DataTemplate>
			<StackLayout>
				<Image Aspect="AspectFit" HeightRequest="50" Source="logo.png" Opacity="1" />
				<BoxView Color="#f0f0f0" HeightRequest="1"/>
			</StackLayout>
		</DataTemplate>
	</Shell.FlyoutHeaderTemplate>


</Shell>
