{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Mraznicka 1.5.3/1.0.0": {"dependencies": {"NETStandard.Library": "2.0.3", "Plugin.NFC": "0.1.22", "Plugin.Toast": "2.2.0", "Xam.Plugin.SimpleAudioPlayer": "1.6.0", "Xamarin.CommunityToolkit": "2.0.5", "Xamarin.Essentials": "1.7.3", "Xamarin.Forms": "5.0.0.2401", "ZXing.Net.Mobile.Forms": "2.4.1", "sqlite-net-pcl": "1.8.116"}, "runtime": {"Mraznicka 1.5.3.dll": {}}, "resources": {"en/Mraznicka 1.5.3.resources.dll": {"locale": "en"}, "cs/Mraznicka 1.5.3.resources.dll": {"locale": "cs"}, "sk/Mraznicka 1.5.3.resources.dll": {"locale": "sk"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Plugin.NFC/0.1.22": {"runtime": {"lib/netstandard2.0/Plugin.NFC.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Plugin.Toast/2.2.0": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard/Plugin.Toast.Abstractions.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}, "lib/netstandard/Plugin.Toast.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "sqlite-net-pcl/1.8.116": {"dependencies": {"SQLitePCLRaw.bundle_green": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLite-net.dll": {"assemblyVersion": "1.8.116.0", "fileVersion": "1.8.116.0"}}}, "SQLitePCLRaw.bundle_green/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.e_sqlite3": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.core/2.0.4": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {}, "SQLitePCLRaw.provider.e_sqlite3/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "System.Buffers/4.4.0": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}}, "System.Memory/4.5.3": {"dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "4.0.1.1", "fileVersion": "4.6.27617.2"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.4.0", "fileVersion": "4.6.26515.6"}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"runtime": {"lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"dependencies": {"System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.4.1", "fileVersion": "4.6.28619.1"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}, "Xam.Plugin.SimpleAudioPlayer/1.6.0": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.0/Plugin.SimpleAudioPlayer.Abstractions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/netstandard1.0/Plugin.SimpleAudioPlayer.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.CommunityToolkit/2.0.5": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4", "Xamarin.Forms": "5.0.0.2401"}, "runtime": {"lib/netstandard2.0/Xamarin.CommunityToolkit.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Essentials/1.7.3": {"dependencies": {"System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/Xamarin.Essentials.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Forms/5.0.0.2401": {"runtime": {"lib/netstandard2.0/Xamarin.Forms.Core.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Platform.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Xaml.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}}}, "ZXing.Net.Mobile/2.4.1": {"runtime": {"lib/netstandard1.0/ZXing.Net.Mobile.Core.dll": {"assemblyVersion": "2.4.1.0", "fileVersion": "2.4.1.0"}, "lib/netstandard1.0/ZXingNetMobile.dll": {"assemblyVersion": "2.4.1.0", "fileVersion": "2.4.1.0"}, "lib/netstandard1.0/zxing.portable.dll": {"assemblyVersion": "0.16.2.0", "fileVersion": "0.16.2.0"}}}, "ZXing.Net.Mobile.Forms/2.4.1": {"dependencies": {"Xamarin.Forms": "5.0.0.2401", "ZXing.Net.Mobile": "2.4.1"}, "runtime": {"lib/netstandard1.0/ZXing.Net.Mobile.Forms.dll": {"assemblyVersion": "2.4.1.0", "fileVersion": "2.4.1.0"}}}}}, "libraries": {"Mraznicka 1.5.3/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Plugin.NFC/0.1.22": {"type": "package", "serviceable": true, "sha512": "sha512-iOZ5gSz4hecL/v16hvOxzljxs0cdMI0qZ1X3cBI9QbVWS6cG1nOGy+2OhUaSVjrVZp3AlQLWg2GpprmVY5mKvQ==", "path": "plugin.nfc/0.1.22", "hashPath": "plugin.nfc.0.1.22.nupkg.sha512"}, "Plugin.Toast/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-PDq+r0P/WRu4YZimpwwfeLaTpHCVWlcw3MjJRC1be6Cg4ORfXL8YDz3HchnrRssRueUgP9ouwzLaQ4tLCD3Q4w==", "path": "plugin.toast/2.2.0", "hashPath": "plugin.toast.2.2.0.nupkg.sha512"}, "sqlite-net-pcl/1.8.116": {"type": "package", "serviceable": true, "sha512": "sha512-W0NuwAOVVAR9LP4eZwNBIrim1p3EN7t8iNfSHXEhtzKAd4YyItekoQ8NyWYs4faVSrN2KZr/P5u4hycCjKKexg==", "path": "sqlite-net-pcl/1.8.116", "hashPath": "sqlite-net-pcl.1.8.116.nupkg.sha512"}, "SQLitePCLRaw.bundle_green/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ubFgOHDmtTcq2LU7Ss10yHnFVMm2rFVr65Ggi+Mh514KjGx4pal98P2zSvZtWf3wbtJw6G1InBgeozBtnpEfKQ==", "path": "sqlitepclraw.bundle_green/2.0.4", "hashPath": "sqlitepclraw.bundle_green.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "path": "sqlitepclraw.core/2.0.4", "hashPath": "sqlitepclraw.core.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YiqCw+PfiPIuX2aZnEqt1ya5ldGm4ParIqzaRXtztDu8mdzZxOXVYOrKZazXg6nbwYbCP1H5mOYGmBFQ2W0M5g==", "path": "sqlitepclraw.provider.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.0.4.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3TIsJhD1EiiT0w2CcDMN/iSSwnNnsrnbzeVHSKkaEgV85txMprmuO+Yq2AdSbeVGcg28pdNDTPK87tJhX7VFHw==", "path": "system.runtime.compilerservices.unsafe/4.5.3", "hashPath": "system.runtime.compilerservices.unsafe.4.5.3.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Xam.Plugin.SimpleAudioPlayer/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-y+ARGxKc+ABuLuzyuWPH3gXxJQJWwXSYMF+fAh8KlwvT7xGc0b/7uAb8kgPW5Yi+VaAHsdFhccAsy0qrQgupsQ==", "path": "xam.plugin.simpleaudioplayer/1.6.0", "hashPath": "xam.plugin.simpleaudioplayer.1.6.0.nupkg.sha512"}, "Xamarin.CommunityToolkit/2.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-8oFY5ZrKoIOCqtTeRh+jx5TAcs177TU0pEoefMWRptrq+TZoKwsPNg7L8lBtLSP+2ymg4VBn8i/6chA6b4mGdQ==", "path": "xamarin.communitytoolkit/2.0.5", "hashPath": "xamarin.communitytoolkit.2.0.5.nupkg.sha512"}, "Xamarin.Essentials/1.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-b6NfxOfkmMGN54a4OcV/D7KXclWWqFwgixMPzlAku3kfKgUBGW4vBdNaujIBmk57NbGpWB2IS78ek0gDHNKwoQ==", "path": "xamarin.essentials/1.7.3", "hashPath": "xamarin.essentials.1.7.3.nupkg.sha512"}, "Xamarin.Forms/5.0.0.2401": {"type": "package", "serviceable": true, "sha512": "sha512-fk/V8QWjeyo8UXcsqsNRMd6o1H5PBfyXYm97nxvhMsJIdeqDgfRWyIsSurM8uLGLQGdQP23R+hHj7vcTSo2UjQ==", "path": "xamarin.forms/5.0.0.2401", "hashPath": "xamarin.forms.5.0.0.2401.nupkg.sha512"}, "ZXing.Net.Mobile/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-iCL2WcqOyUnC1iFFdzXijk0EXfoLMDqW88MyhoTZigSyhJZM2C5hKgSZVyfx9E35tgkde/IZwHkk8cVDnbQlYg==", "path": "zxing.net.mobile/2.4.1", "hashPath": "zxing.net.mobile.2.4.1.nupkg.sha512"}, "ZXing.Net.Mobile.Forms/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-7z+MfL8TbfE36s1GGvrnlkhLUOV5dgdn1gYhUb3GU2W6YXzpfF39k6EqkmFsyNm1O07evTaKFgh7UTOqcWub3g==", "path": "zxing.net.mobile.forms/2.4.1", "hashPath": "zxing.net.mobile.forms.2.4.1.nupkg.sha512"}}}