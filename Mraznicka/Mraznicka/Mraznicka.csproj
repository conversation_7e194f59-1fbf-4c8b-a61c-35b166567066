﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
    <AssemblyName>Mraznicka 1.5.3</AssemblyName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="beep-02.mp3" />
    <None Remove="MyData.db" />
    <None Remove="No No.mp3" />
    <None Remove="Pubg - ok.mp3" />
    <None Remove="Resources\No No.mp3" />
    <None Remove="Resources\Pubg - ok.mp3" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="beep-02.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="No No.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Pubg - ok.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\beep-02.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="MyData.db">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\No No.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\Pubg - ok.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Plugin.NFC" Version="0.1.22" />
    <PackageReference Include="Plugin.Toast" Version="2.2.0" />
    <PackageReference Include="sqlite-net-pcl" Version="1.8.116" />
    <PackageReference Include="Xam.Plugin.SimpleAudioPlayer" Version="1.6.0" />
    <PackageReference Include="Xamarin.CommunityToolkit" Version="2.0.5" />
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.3" />
    <PackageReference Include="ZXing.Net.Mobile.Forms" Version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\AppResources .en.Designer.cs">
      <DependentUpon>AppResources .en.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="Resources\AppResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>AppResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Views\AboutPage.xaml.cs">
      <DependentUpon>AboutPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\PushPage.xaml.cs">
      <DependentUpon>PushPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\PreviewPage.xaml.cs">
      <DependentUpon>PreviewPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\PullPage.xaml.cs">
      <DependentUpon>PullPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\MainPage.xaml.cs">
      <DependentUpon>MainPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Setting\FactoryPage.xaml.cs">
      <DependentUpon>FactoryPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Tovar\CreatePage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Tovar\DetailPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Tovar\ListPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Polozka\CreatePage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Polozka\DetailPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Polozka\ListPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\AllInOnePageTag.xaml.cs">
      <DependentUpon>AllInOnePageTag.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\AllInOnePageEan.xaml.cs">
      <DependentUpon>AllInOnePageEan.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\ExpressPage.xaml.cs">
      <DependentUpon>ExpressPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\AllInOnePage.xaml.cs">
      <DependentUpon>AllInOnePage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\TagPage.xaml.cs">
      <DependentUpon>TagPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\ManualPage.xaml.cs">
      <DependentUpon>ManualPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vlozenie\EANCodePage.xaml.cs">
      <DependentUpon>EANCodePage.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Update="Views\Vyber\CompareTagPage.xaml.cs">
      <DependentUpon>CompareTagPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\CompareEANPage.xaml.cs">
      <DependentUpon>CompareEANPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\EANCodePage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\CompareManualPage.xaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>CompareManualPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\ManualPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\ExpressPage.xaml.cs">
      <DependentUpon>ExpressPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Vyber\TagPage.xaml.cs">
      <DependentUpon>TagPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Zariadenie\CreatePage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Zariadenie\DetailPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Zariadenie\ListPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Pozicia\CreatePage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Pozicia\DetailPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Pozicia\ListPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Miestnost\DetailPage.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Views\Miestnost\ListPage.xaml.cs">
      <DependentUpon>ListPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Miestnost\CreatePage.xaml.cs">
      <DependentUpon>CreatePage.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="AppShell.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Footer.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\AppResources .en.resx">
      <SubType>Designer</SubType>
      <LastGenOutput>AppResources .en.Designer.cs</LastGenOutput>
      <Generator>ResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\AppResources.cs.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\AppResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>AppResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\AboutPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\LoginPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Polozka\DetailPageZmazanie.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Vlozenie\EANCodePage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Vyber\CompareManualPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Views\NewFolder\" />
    <Folder Include="Views\Setting\" />
  </ItemGroup>
</Project>