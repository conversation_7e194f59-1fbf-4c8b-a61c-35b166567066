﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Mraznicka.Views.Miestnost.CreatePage"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate novamiestnost}">

	<RelativeLayout>
		<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>
		
		<RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
			<!--<Image Source="istockphoto1.jpg" Opacity="1" Aspect="AspectFit" VerticalOptions="EndAndExpand"></Image>-->
			<Label Margin="20" TextColor="#666" WidthRequest="230" LineBreakMode="WordWrap" Text="Zoznam miestností s chladni<PERSON>, mrazničkou Vám ponúka prehladný zoznam spolu s obsadenostou daného zariadenia. Takto prehladne vidíte kolko miesta máte ešte volného."></Label>
		</RelativeLayout>
		
		<StackLayout Margin="0,150,0,80" Padding="20" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height}">
			<Entry Text="{Binding Item.Nazov, Mode=TwoWay}" Style="{StaticResource EntryStyle}" Placeholder="Zadajte názov miestnosti"  />
		</StackLayout>
        <RelativeLayout  RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-110}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
            <StackLayout>
                <StackLayout Orientation="Horizontal" >
                    <Button Text="{i18n:Translate ulozit}" Image="icon_about.png" ContentLayout="Left,20" Command="{Binding SaveCommand}" HorizontalOptions="FillAndExpand" Margin="0"></Button>
                    <Button Text="{i18n:Translate zrusit}" Image="icon_about.png" ContentLayout="Left,20" Command="{Binding CancelCommand}" HorizontalOptions="FillAndExpand" Margin="0"></Button>
                </StackLayout>
                <Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFit" ></Image>
            </StackLayout>
        </RelativeLayout>
    </RelativeLayout>

</ContentPage>
