﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:zxing="clr-namespace:ZXing.Net.Mobile.Forms;assembly=ZXing.Net.Mobile.Forms"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.Vlozenie.AllInOnePage"
			 xmlns:converters="clr-namespace:Mraznicka.ValueConverters"             
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate vlozenie}">

    <ContentPage.Resources>
        <converters:TovarToImageConverter x:Key="tovarToImageConverter" />
        <converters:TovarLocalizeConverter x:Key="tovarLocalizeConverter" />
    </ContentPage.Resources>

    <RelativeLayout>
		<!--<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>-->

		<!--<RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
			<Label Style="{StaticResource Note}" Text="{i18n:Translate popisvlozeniemanual}"></Label>
		</RelativeLayout>-->

		<ScrollView Margin="20,0,20,130" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
			<StackLayout>

                <StackLayout Orientation="Horizontal">
                    <Label 
                        Text="{i18n:Translate kategoria}" 
                        Style="{DynamicResource KeyLabel}" 
                        FontSize="20" 
                        FontAttributes="None" 
                        MinimumWidthRequest="150"/>
                    <Picker x:Name="pckTovar" 
                            FontSize="20" 
                            Title="{i18n:Translate tovar}" 
                            Style="{StaticResource ValuePicker}" 
                            ItemsSource="{Binding Tovary}" 
                            ItemDisplayBinding="{Binding Nazov}" 
                            SelectedItem="{Binding SelectedData.Tovar}" 
                            TextColor="Black" 
                            HorizontalOptions="FillAndExpand"
                            FontAttributes="Bold">
                    </Picker>
                </StackLayout>

                <StackLayout IsVisible="{Binding ShowDetail, Mode=TwoWay}">
                    <StackLayout Margin="0,0,0,0" Orientation="Horizontal">
                        <Label Text="{i18n:Translate popis}" Style="{DynamicResource KeyLabel}" TextColor="Black" FontSize="16" FontAttributes="None" MinimumWidthRequest="150" Padding="0,4"/>
                        <Entry TextColor="Black" FontSize="20" Text="{Binding Item.Popis, Mode=TwoWay}" Style="{DynamicResource ValueEntry}" Placeholder="{i18n:Translate zadajte_popis}" PlaceholderColor="Red" HorizontalOptions="FillAndExpand" FontAttributes="Bold"/>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate zariadenie}" Style="{DynamicResource KeyLabel}" FontSize="16" FontAttributes="None" MinimumWidthRequest="150" Padding="0,4"/>
                        <Picker x:Name="pckZariadenie" Title="{i18n:Translate zariadenie}" Style="{StaticResource ValuePicker}" ItemsSource="{Binding Zariadenia}" ItemDisplayBinding="{Binding Nazov}" SelectedItem="{Binding SelectedData.Zariadenie}" SelectedIndex="{Binding SelectedZariadenieIndex}" WidthRequest="300" TextColor="Black" FontSize="20" FontAttributes="Bold"></Picker>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate pozicia}" Style="{DynamicResource KeyLabel}" FontSize="16" FontAttributes="None" MinimumWidthRequest="120" Padding="0,4"/>
                        <Picker x:Name="pckPozicia" Title="{i18n:Translate pozicia}" Style="{StaticResource ValuePicker}" ItemsSource="{Binding Pozicie}" ItemDisplayBinding="{Binding Nazov}" SelectedItem="{Binding SelectedData.Pozicia}" SelectedIndex="{Binding SelectedPoziciaIndex}" WidthRequest="300" TextColor="Black" FontSize="20" FontAttributes="Bold"></Picker>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate hmotnost}" Style="{DynamicResource KeyLabel}" FontSize="16" FontAttributes="None" MinimumWidthRequest="180" Padding="0,4"/>
                        <Entry Text="{Binding Item.Hmotnost, StringFormat='{0}'}" Style="{DynamicResource EntryStyle}" Placeholder="{i18n:Translate zadajtehmotnost}" Keyboard="Numeric" FontAttributes="Bold" WidthRequest="300"/>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate poznamka}" Style="{DynamicResource KeyLabel}" FontSize="16" FontAttributes="None" MinimumWidthRequest="150" Padding="0,4"/>
                        <Entry Text="{Binding Item.Poznamka, Mode=TwoWay}" Style="{StaticResource EntryStyle}" Placeholder="{i18n:Translate zadajte_poznamku}" FontSize ="20" FontAttributes="Bold" WidthRequest="300"/>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate vlozenie}" FontSize="16" Style="{DynamicResource KeyLabel}" FontAttributes="None" MinimumWidthRequest="120" Padding="0,0"/>
                        <Label Text="{Binding Item.DatumVytvorenia}" Style="{DynamicResource ValueLabel}"  FontSize="16" FontAttributes="Bold" WidthRequest="250"/>
                    </StackLayout>

                    <StackLayout Orientation="Horizontal">
                        <Label Text="{i18n:Translate expiracia}" FontSize="16" Style="{DynamicResource KeyLabel}" FontAttributes="None" Padding="0,5"/>
                        <DatePicker Date="{Binding Item.Expiracia, Mode=TwoWay}" FontSize="20" TextColor="Black" FontAttributes="Bold"/>
                    </StackLayout>


                    <!--<StackLayout Orientation="Horizontal" Margin="0,50,0,50" HorizontalOptions="Center">
                        <Button Text="T" Image="ikona_tag50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding TagCommand}"></Button>
                        <Button Text="E" Image="ikona_ean50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding EanCommand}"></Button>
                        <Button Text="M" Image="ikona_ruka50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding ManualCommand}"></Button>
                    </StackLayout>-->

                </StackLayout>

                <!--<StackLayout Margin="20,0,20,0" IsVisible="{Binding InsertByEan, Mode=TwoWay}">
					<zxing:ZXingScannerView  IsScanning="true" HeightRequest="200"  IsAnalyzing="True" ScanResultCommand="{Binding ScanResultCommand}" />
					<Label Text="{Binding Item.TagID, StringFormat='ID/EAN: {0}'}" TextColor="LightGray"  />
				</StackLayout>

				<StackLayout Margin="20,0,20,0" IsVisible="{Binding InsertByTag, Mode=TwoWay}">
					<Image  HorizontalOptions="Center" Source="nfc.png" Opacity="0.8" Margin="0,50,0,0" HeightRequest="145" Aspect="AspectFill" ></Image>
				</StackLayout>-->


			</StackLayout>
		</ScrollView>
        <RelativeLayout  RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-110}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
            <StackLayout>
                <StackLayout Orientation="Horizontal" >
                    <Button Text="T" Image="ikona_tag50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding TagCommand}"></Button>
                    <Button Text="E" Image="ikona_ean50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding EanCommand}"></Button>
                    <Button Text="M" Image="ikona_ruka50x50.png" ContentLayout="Left,20" Margin="0" Command="{Binding ManualCommand}"></Button>
                </StackLayout>
                <Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFit" ></Image>
            </StackLayout>
        </RelativeLayout>
    </RelativeLayout>
</ContentPage>