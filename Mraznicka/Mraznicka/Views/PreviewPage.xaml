﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
			 xmlns:local="clr-namespace:Mraznicka.ViewModels"  
             xmlns:model="clr-namespace:Mraznicka.Models" 
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:converters="clr-namespace:Mraznicka.ValueConverters"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.PreviewPage"
             xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate prehlad}" >

	<ContentPage.Resources>
		<converters:ZariadenieConverter x:Key="zariadenieConverter" />
		<converters:MiestnostConverter x:Key="miestnostConverter" />
		<converters:PoziciaConverter x:Key="poziciaConverter" />
		<converters:TovarConverter x:Key="tovarConverter" />
		<converters:DateConverter x:Key="dateConverter" />
        <converters:DayConverter x:Key="dayConverter" />
        <converters:ExpiraciaConverter x:Key="expiraciaConverter" />
        <converters:InsertConverter x:Key="insertConverter" />
        <converters:TovarToImageConverter x:Key="tovarToImageConverter" />

    </ContentPage.Resources>
	<RelativeLayout>
		<StackLayout Margin="10,0,0,0">
            <Label TextColor="Black" Text="{i18n:Translate filter}" FontSize="18" FontAttributes="Bold"></Label>
            <Picker x:Name="pckTovar" Title="{i18n:Translate tovar}" TextColor="Gray" FontSize="18" ItemsSource="{Binding Tovary}" ItemDisplayBinding="{Binding Nazov}" WidthRequest="400" SelectedIndexChanged="Handle_SelectedTovarIndexChanged"></Picker>
            <Picker x:Name="pckZariadenie"  Title="{i18n:Translate zariadenie}" TextColor="Gray" FontSize="18" ItemsSource="{Binding Zariadenia}" ItemDisplayBinding="{Binding Nazov}" WidthRequest="400" SelectedIndexChanged="Handle_SelectedZariadenieIndexChanged"></Picker>
            <Entry Placeholder="{i18n:Translate zadajte_opis_popisu}" TextColor="Gray" FontSize="18" PlaceholderColor="Gray" Text="{Binding Filter}" WidthRequest="400"></Entry>
		</StackLayout>

		<RefreshView Margin="0,220,0,0"  x:DataType="local:PreviewPageViewModel" Command="{Binding LoadItemsCommand}" IsRefreshing="{Binding IsBusy, Mode=TwoWay}">
			<CollectionView  x:Name="ItemsListView" ItemsSource="{Binding Items}" SelectionMode="None">
				<CollectionView.ItemTemplate>
					<DataTemplate>
                        <StackLayout Padding="20,0,0,0" x:DataType="model:Polozka" Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">

                            <StackLayout Orientation="Vertical" WidthRequest="90" >
                                <Label Text="{i18n:Translate vyber}" TextColor="Black" Margin="5,5,0,0" FontSize="12"/>
                                <ImageButton Source="{Binding Tovar, Converter={StaticResource tovarToImageConverter}}" 
                                    Margin="0,-25,20,0" 
                                    VerticalOptions="CenterAndExpand" 
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type local:PreviewPageViewModel}}, Path=ItemPull}" 
                                    CommandParameter="{Binding .}"></ImageButton>
                            </StackLayout>

                            <StackLayout>
								<Label Text="{Binding Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" FontSize="20"/>
								<StackLayout Orientation="Horizontal">
									<Frame Style="{DynamicResource FrameOrange}" 
                                        BackgroundColor="{Binding ., Converter={StaticResource expiraciaConverter}}" 
                                        BorderColor="{Binding ., Converter={StaticResource expiraciaConverter}}">
										<Label Text="{Binding Expiracia, StringFormat='{0}', 
                                            Converter={StaticResource dayConverter}}"  
                                            Style="{DynamicResource FrameLabelWhite}"
                                            FontSize="15"/>
									</Frame>
								</StackLayout>
								<StackLayout Orientation="Horizontal">
                                    <Label Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15"
                                           FontAttributes="None" Padding="0,-4,0,0"/>

                                    <Label Text="{Binding DatumVytvorenia, StringFormat='{0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15" FontAttributes="None" Padding="0,-4,0,0"/>
                                    
									<Label TextColor="Blue" 
                                       FontSize="15"
                                       Text="{Binding Typ, StringFormat='{0}', Converter={StaticResource insertConverter}}" 
                                       Style="{DynamicResource KeyLabel}" Padding="0,-4,0,0"/>
								</StackLayout>

                        </StackLayout>

							<StackLayout.GestureRecognizers>
                                <TapGestureRecognizer NumberOfTapsRequired="1" Command="{Binding Source={RelativeSource AncestorType={x:Type local:PreviewPageViewModel}}, Path=ItemTapped}" CommandParameter="{Binding .}"></TapGestureRecognizer>
                            </StackLayout.GestureRecognizers>
						</StackLayout>
					</DataTemplate>
				</CollectionView.ItemTemplate>
			</CollectionView>
		</RefreshView>

    </RelativeLayout>
</ContentPage>