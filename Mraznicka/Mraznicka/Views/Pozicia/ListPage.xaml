﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Mraznicka.Views.Pozicia.ListPage"
             xmlns:local="clr-namespace:Mraznicka.ViewModels.Pozicia"  
             xmlns:model="clr-namespace:Mraznicka.Models"
			 BackgroundColor="#F2F7FA"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate zoznampozicii}">


	<ContentPage.ToolbarItems>
		<ToolbarItem Text="Add" Command="{Binding AddItemCommand}" />
	</ContentPage.ToolbarItems>


	<RelativeLayout>
		<!--<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>

		<RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
			<Label Style="{StaticResource Note}" Text="{i18n:Translate popispozicialist}"></Label>
		</RelativeLayout>-->

		<RefreshView Margin="0,0,0,80"  x:DataType="local:ListViewModel" Command="{Binding LoadItemsCommand}" IsRefreshing="{Binding IsBusy, Mode=TwoWay}">
			<CollectionView x:Name="ItemsListView" ItemsSource="{Binding Items}" SelectionMode="None">
				<CollectionView.ItemTemplate>
					<DataTemplate>
						<StackLayout x:DataType="model:Pozicia" Style="{DynamicResource StackLayoutList}">
							<Label Text="{Binding Nazov}" Style="{DynamicResource Header}" />
							<!--<StackLayout Orientation="Horizontal">
								<Label Text="Vlozene: 18.12.2021"  Style="{DynamicResource FrameLabel}" />
							</StackLayout>-->
							<StackLayout.GestureRecognizers>
								<TapGestureRecognizer NumberOfTapsRequired="1" Command="{Binding Source={RelativeSource AncestorType={x:Type local:ListViewModel}}, Path=ItemTapped}" CommandParameter="{Binding .}"></TapGestureRecognizer>
							</StackLayout.GestureRecognizers>
						</StackLayout>
					</DataTemplate>
				</CollectionView.ItemTemplate>
			</CollectionView>
		</RefreshView>
		<RelativeLayout RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-110}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
			<StackLayout>
                <Button Text="{i18n:Translate pridat}" Image="ikona_pridat50x50.png" ContentLayout="Left,20" Command="{Binding AddItemCommand}" HorizontalOptions="FillAndExpand" Margin="0"></Button>
				<Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFill" ></Image>
			</StackLayout>
		</RelativeLayout>
	</RelativeLayout>




</ContentPage>
