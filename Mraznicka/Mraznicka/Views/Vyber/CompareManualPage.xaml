﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.Vyber.CompareManualPage"
			 xmlns:converters="clr-namespace:Mraznicka.ValueConverters"
			 xmlns:local="clr-namespace:Mraznicka.ViewModels.Vyber"
			 xmlns:model="clr-namespace:Mraznicka.Models"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate manualnyvyber}">

    <ContentPage.Resources>
        <converters:ZariadenieConverter x:Key="zariadenieConverter" />
        <converters:MiestnostConverter x:Key="miestnostConverter" />
        <converters:PoziciaConverter x:Key="poziciaConverter" />
        <converters:TovarConverter x:Key="tovarConverter" />
        <converters:DateConverter x:Key="dateConverter" />
        <converters:DayConverter x:Key="dayConverter" />
        <converters:ExpiraciaConverter x:Key="expiraciaConverter" />
        <converters:SerialNumberConverter x:Key="serialnumberConverter" />
		<converters:TovarToImageConverter x:Key="tovarToImageConverter" />
    </ContentPage.Resources>

    <RelativeLayout>
        <!--<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>

        <RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
            <Label Style="{StaticResource Note}" Text="Zoznam miestností s chladničkou, mrazničkou Vám ponúka prehladný zoznam spolu s obsadenostou daného zariadenia. Takto prehladne vidíte kolko miesta máte ešte volného."></Label>
        </RelativeLayout>-->

        <ScrollView Margin="20" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
            <StackLayout>
                <StackLayout Padding="0,0,0,0"  Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">

                    <StackLayout Orientation="Vertical" WidthRequest="80">
                        <ImageButton Source="{Binding Item.Tovar, Converter={StaticResource tovarToImageConverter}}"  Margin="0,0,20,0" VerticalOptions="CenterAndExpand"></ImageButton>
                    </StackLayout>

                    <StackLayout>
                        <StackLayout.GestureRecognizers>
                            <!--<TapGestureRecognizer Command="{Binding ItemTapped}" CommandParameter="{Binding Item}"/>-->

                        </StackLayout.GestureRecognizers>
                        <Label Text="{Binding Item.Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" />
                        <StackLayout Orientation="Horizontal">
                            <Frame Style="{DynamicResource FrameOrange}" BackgroundColor="{Binding Item, Converter={StaticResource expiraciaConverter}}" BorderColor="{Binding Item, Converter={StaticResource expiraciaConverter}}">
                                <Label Text="{Binding Item.Expiracia, StringFormat='{0}', Converter={StaticResource dayConverter}}"  Style="{DynamicResource FrameLabelWhite}" FontSize="15"/>
                            </Frame>
                        </StackLayout>
                        <StackLayout Orientation="Horizontal">
                            <Label Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15"
                                           FontAttributes="None" Padding="0,-6,0,0"/>

                            <Label Text="{Binding Item.DatumVytvorenia, StringFormat='{0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15" FontAttributes="None" Padding="0,-4,0,0"/>

                        </StackLayout>
                    </StackLayout>
                </StackLayout>

                <Button x:Name="btnSubmit" Text="{i18n:Translate vyber}" Image="ikona_vybrat50x50" Command="{Binding VyberCommand}" Margin="0,20,0,0" HorizontalOptions="FillAndExpand"></Button>

            </StackLayout>
        </ScrollView>
        <RelativeLayout RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-60}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
            <Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFill" ></Image>
        </RelativeLayout>
    </RelativeLayout>

</ContentPage>