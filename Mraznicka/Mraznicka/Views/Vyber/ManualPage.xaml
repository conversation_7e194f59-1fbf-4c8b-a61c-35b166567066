﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.Vyber.ManualPage"
			 xmlns:converters="clr-namespace:Mraznicka.ValueConverters"
			  xmlns:local="clr-namespace:Mraznicka.ViewModels.Vyber"
			 xmlns:model="clr-namespace:Mraznicka.Models"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate manualnyvyber}">

    <ContentPage.Resources>
		<converters:ZariadenieConverter x:Key="zariadenieConverter" />
		<converters:MiestnostConverter x:Key="miestnostConverter" />
		<converters:PoziciaConverter x:Key="poziciaConverter" />
		<converters:TovarConverter x:Key="tovarConverter" />
		<converters:DateConverter x:Key="dateConverter" />
        <converters:DayConverter x:Key="dayConverter" />
        <converters:ExpiraciaConverter x:Key="expiraciaConverter" />
		<converters:InsertConverter x:Key="insertConverter" />
		<converters:TovarToImageConverter x:Key="tovarToImageConverter" />

	</ContentPage.Resources>

    <RelativeLayout>
        <StackLayout Margin="10,0,0,0">
            <Label TextColor="Black" Text="Filter" FontSize="18" FontAttributes="Bold"></Label>
            <Picker x:Name="pckZariadenie"  Title="{i18n:Translate vyberte_zariadenia_vsetky}" TextColor="Gray" FontSize="18" ItemsSource="{Binding Zariadenia}" ItemDisplayBinding="{Binding Nazov}" WidthRequest="400" SelectedIndexChanged="Handle_SelectedZariadenieIndexChanged"></Picker>
        </StackLayout>

        <ScrollView Margin="0,100,0,0" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
            <StackLayout>
                <CollectionView BackgroundColor="White" x:Name="ItemsListView" ItemsSource="{Binding Items}" SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>

							<StackLayout Padding="20,10,0,0" x:DataType="model:Polozka" Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">

                                <StackLayout Orientation="Vertical" WidthRequest="80">
                                    <Label Text="{i18n:Translate vyber}" TextColor="Black" Margin="10,15,0,0" Padding="0,10,0,0"/>
                                    <ImageButton Source="{Binding Tovar, Converter={StaticResource tovarToImageConverter}}"  
                                                 Command="{Binding Source={RelativeSource AncestorType={x:Type local:ManualPageViewModel}}, Path=ItemTapped}" CommandParameter="{Binding .}" 
                                                 Margin="0,-70,20,0" VerticalOptions="CenterAndExpand"></ImageButton>
                                </StackLayout>

                                <StackLayout>
									<Label Text="{Binding Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" FontSize="25"/>
									<StackLayout>
                                        <StackLayout>
                                            <Label Text="{Binding Zariadenie, Converter={StaticResource zariadenieConverter}}"  FontSize="20" TextColor="Black" />
                                        </StackLayout>

                                        <StackLayout>
                                            <Label Text="{Binding Pozicia, Converter={StaticResource poziciaConverter}}"  FontSize="20" TextColor="Black" />
                                        </StackLayout>
                                        <Frame Style="{DynamicResource FrameOrange}" BackgroundColor="{Binding ., Converter={StaticResource expiraciaConverter}}" BorderColor="{Binding ., Converter={StaticResource expiraciaConverter}}">
                                            <Label Text="{Binding Expiracia, StringFormat='{0}', Converter={StaticResource dayConverter}}"  Style="{DynamicResource FrameLabelWhite}" FontSize="15"/>
										</Frame>
									</StackLayout>
                                    <StackLayout Orientation="Horizontal">
                                        <Label Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black"/>

                                        <Label Text="{Binding DatumVytvorenia, StringFormat=': {0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black"/>
                                        <Label TextColor="Blue" 
                                       FontSize="15"
                                       Text="{Binding Typ, StringFormat='{0}', Converter={StaticResource insertConverter}}" 
                                       Style="{DynamicResource KeyLabel}" />
                                    </StackLayout>
                                </StackLayout>

								<StackLayout.GestureRecognizers>
									<TapGestureRecognizer NumberOfTapsRequired="1" Command="{Binding Source={RelativeSource AncestorType={x:Type local:ManualPageViewModel}}, Path=ItemTapped}" CommandParameter="{Binding .}"></TapGestureRecognizer>
								</StackLayout.GestureRecognizers>
							</StackLayout>
						</DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </StackLayout>
        </ScrollView>
    </RelativeLayout>

</ContentPage>