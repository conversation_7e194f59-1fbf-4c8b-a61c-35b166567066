﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.Vyber.CompareTagPage"
             xmlns:converters="clr-namespace:Mraznicka.ValueConverters"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate vyberpomocoutag}">

    <ContentPage.Resources>
        <converters:TovarConverter x:Key="tovarConverter" />
		<converters:TovarToImageConverter x:Key="tovarToImageConverter" />
		<converters:DateConverter x:Key="dateConverter" />
        <converters:DayConverter x:Key="dayConverter" />
        <converters:ExpiraciaConverter x:Key="expiraciaConverter" />
	</ContentPage.Resources>

    <RelativeLayout>
        <!--<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>

        <RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
            <Label Style="{StaticResource Note}" Text="Zoznam miestností s chladničkou, mrazničkou Vám ponúka prehladný zoznam spolu s obsadenostou daného zariadenia. Takto prehladne vidíte kolko miesta máte ešte volného."></Label>
        </RelativeLayout>-->

        <ScrollView Margin="20" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
            <StackLayout Margin="10,0,0,200">
                <Label Style="{DynamicResource Header}" Text="{i18n:Translate hladana_polozka}" HorizontalOptions="CenterAndExpand" FontSize="25"/>
                <StackLayout Padding="0,0,0,0"  Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">

					<StackLayout Orientation="Vertical" WidthRequest="80">
						<ImageButton Source="{Binding Item.Tovar, Converter={StaticResource tovarToImageConverter}}"  Margin="0,0,20,0" VerticalOptions="CenterAndExpand"></ImageButton>
					</StackLayout>

					<StackLayout>
						<StackLayout.GestureRecognizers>
							<!--<TapGestureRecognizer Command="{Binding ItemTapped}" CommandParameter="{Binding Item}"/>-->

						</StackLayout.GestureRecognizers>
						<Label Text="{Binding Item.Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" FontSize="20"/>
						<StackLayout Orientation="Horizontal">
							<Frame Style="{DynamicResource FrameOrange}" BackgroundColor="{Binding Item, Converter={StaticResource expiraciaConverter}}" BorderColor="{Binding Item, Converter={StaticResource expiraciaConverter}}">
								<Label Text="{Binding Item.Expiracia, StringFormat='{0}', Converter={StaticResource dayConverter}}"  Style="{DynamicResource FrameLabelWhite}" />
							</Frame>
						</StackLayout>
                        <StackLayout Orientation="Horizontal">
                            <Label Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15"
                                           FontAttributes="None" Padding="0,-4,0,0"/>

                            <Label Text="{Binding Item.DatumVytvorenia, StringFormat='{0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15" FontAttributes="None" Padding="0,-4,0,0"/>

                        </StackLayout>
                    </StackLayout>
				</StackLayout>

                <Label x:Name="Najdena" Text="{i18n:Translate najdena_polozka}"  Style="{DynamicResource Header}" HorizontalOptions="CenterAndExpand" TextColor="Black" FontSize="25"/>
                <!--<StackLayout Orientation="Vertical" Margin="0,0,0,0">
                        <Label Text="Popis:" FontSize="16" TextColor="Black" FontAttributes="Bold" />
                        <Label x:Name="TagPopis" Text="{Binding TagItem.Popis}" FontSize="25" TextColor="Black"  />
                    </StackLayout>

                    <StackLayout Orientation="Horizontal" Margin="0,0,0,0">
                        <Label Text="TagID:" FontSize="16" TextColor="Black" FontAttributes="Bold" />
                        <Label Text="{Binding TagItem.TagID}" FontSize="16" TextColor="Black"  />
                    </StackLayout>-->

				<StackLayout Padding="0,0,0,0"  Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">

					<StackLayout Orientation="Vertical" WidthRequest="80">
						<ImageButton Source="{Binding TagItem.Tovar, Converter={StaticResource tovarToImageConverter}}"  Margin="0,0,20,0" VerticalOptions="CenterAndExpand"></ImageButton>
					</StackLayout>

					<StackLayout>
						<StackLayout.GestureRecognizers>
							<!--<TapGestureRecognizer Command="{Binding ItemTapped}" CommandParameter="{Binding Item}"/>-->

						</StackLayout.GestureRecognizers>
                        <Label x:Name="Popis" Text="{Binding TagItem.Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" TextColor="Black" FontSize="20"/>
						<StackLayout Orientation="Horizontal">
							<Frame Style="{DynamicResource FrameOrange}" BackgroundColor="{Binding TagItem, Converter={StaticResource expiraciaConverter}}" BorderColor="{Binding TagItem, Converter={StaticResource expiraciaConverter}}">
								<Label Text="{Binding TagItem.Expiracia, StringFormat='{0}', Converter={StaticResource dayConverter}}"  Style="{DynamicResource FrameLabelWhite}" />
							</Frame>
						</StackLayout>
                        <StackLayout Orientation="Horizontal">
                            <Label x:Name="Vlozene" Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15"
                                           FontAttributes="None" Padding="0,-4,0,0"/>

                            <Label x:Name="Datum"  Text="{Binding Item.DatumVytvorenia, StringFormat='{0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black" FontSize="15" FontAttributes="None" Padding="0,-4,0,0"/>

                        </StackLayout>
                    </StackLayout>
				</StackLayout>


				<Button x:Name="btnSubmit" Text="{i18n:Translate vyber}" ImageSource="ikona_vybrat50x50" Command="{Binding VyberCommand}" Margin="0,20,0,0" HorizontalOptions="FillAndExpand"></Button>
                <Image  HorizontalOptions="Center" Source="nfc.png" Opacity="0.8" Margin="0,50,0,0" HeightRequest="145" Aspect="AspectFill" ></Image>

            </StackLayout>
        </ScrollView>
        <RelativeLayout RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-60}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
            <Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFill" ></Image>
        </RelativeLayout>
    </RelativeLayout>

</ContentPage>