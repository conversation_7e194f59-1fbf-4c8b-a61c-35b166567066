﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:zxing="clr-namespace:ZXing.Net.Mobile.Forms;assembly=ZXing.Net.Mobile.Forms" 
			 xmlns:model="clr-namespace:Mraznicka.Models"
			 mc:Ignorable="d"
             x:Class="Mraznicka.Views.Vyber.EANCodePage"
			 xmlns:converters="clr-namespace:Mraznicka.ValueConverters"
             xmlns:local="clr-namespace:Mraznicka.ViewModels.Vyber"  
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate vyberpomocouean}">


	<ContentPage.Resources>
		<converters:ZariadenieConverter x:Key="zariadenieConverter" />
		<converters:MiestnostConverter x:Key="miestnostConverter" />
		<converters:PoziciaConverter x:Key="poziciaConverter" />
		<converters:TovarConverter x:Key="tovarConverter" />
		<converters:DateConverter x:Key="dateConverter" />
        <converters:DayConverter x:Key="dayConverter" />
        <converters:ExpiraciaConverter x:Key="expiraciaConverter" />
		<converters:TovarToImageConverter x:Key="tovarToImageConverter" />
	</ContentPage.Resources>

    <RelativeLayout>
        <ScrollView Margin="20,20,20,20" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
			<StackLayout>
				<zxing:ZXingScannerView  x:Name="_scanView" OnScanResult="Handle_OnScanResult" IsScanning="true" HeightRequest="130"  />
				<Label Text="{Binding Item.TagID, StringFormat='EAN: {0}'}" TextColor="LightGray"  />
                <Label Text="{i18n:Translate telefon_ku_ean}" HorizontalOptions="CenterAndExpand" FontSize="20" TextColor="Black"  />
                <Label x:Name="najdena_polozka" Text="{i18n:Translate najdena_polozka}"  FontSize="25" HorizontalOptions="CenterAndExpand" TextColor="Green"/>

                <CollectionView Margin="-20,0,0,0" BackgroundColor="White" x:Name="ItemsListView" ItemsSource="{Binding Items}" SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <StackLayout Padding="20,20,0,0" x:DataType="model:Polozka" Orientation="Horizontal"  Style="{DynamicResource StackLayoutList}" BackgroundColor="#fff">
                                <StackLayout Orientation="Vertical" WidthRequest="100">
                                    <Label Text="{i18n:Translate vyber}" TextColor="Black" Margin="10,-5,0,0" Padding="0,0,0,0"/>
                                    <ImageButton Source="{Binding Tovar, Converter={StaticResource tovarToImageConverter}}"  
                                                 Command="{Binding Source={RelativeSource AncestorType={x:Type local:EANCodePageViewModel}}, Path=ItemPull}" CommandParameter="{Binding .}" 
                                                 Margin="0,-40,20,0" VerticalOptions="CenterAndExpand"></ImageButton>
                                </StackLayout>

                                <StackLayout>
                                    <Label Text="{Binding Popis}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" Margin="0,-10,0,0"/>

                                    <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                                        <Frame Style="{DynamicResource FrameOrange}" BackgroundColor="{Binding ., Converter={StaticResource expiraciaConverter}}" BorderColor="{Binding ., Converter={StaticResource expiraciaConverter}} ">
                                            <Label Text="{Binding Expiracia, StringFormat='{0}', Converter={StaticResource dayConverter}}"  Style="{DynamicResource FrameLabelWhite}" Padding ="0,0,0,0" Margin="0,0,0,0"/>
                                        </Frame>
                                    </StackLayout>
                                    <!--
                                    <StackLayout Orientation="Horizontal">
                                       <Label Text="{i18n:Translate hmotnost}" 
                                            Style="{DynamicResource KeyLabel}" 
                                            TextColor="Black"/>
                                       <Label Text="{Binding Hmotnost, StringFormat=': {0}'}" 
                                            Style="{DynamicResource KeyLabel}" 
                                            TextColor="Black"/>
                                    </StackLayout>
                                    -->

                                    <StackLayout Orientation="Horizontal">
                                        <Label Text="{i18n:Translate vlozene}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black"/>
                                        <Label Text="{Binding DatumVytvorenia, StringFormat=': {0}' , Converter={StaticResource dateConverter}}" 
                                       Style="{DynamicResource KeyLabel}" 
                                       TextColor="Black"/>
                                    </StackLayout>

                                    <Label Text="{Binding Poznamka}" LineBreakMode="NoWrap" Style="{DynamicResource Header}" FontSize="15" MinimumWidthRequest="250" />
                                </StackLayout>

                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer NumberOfTapsRequired="1" Command="{Binding Source={RelativeSource AncestorType={x:Type local:EANCodePageViewModel}}, Path=ItemPull}" CommandParameter="{Binding .}"></TapGestureRecognizer>
                                </StackLayout.GestureRecognizers>
                            </StackLayout>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
            </StackLayout>
        </ScrollView>
        <RelativeLayout RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-80}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="CenterAndExpand">
            <StackLayout>
                <StackLayout>
                    <!-- <Button x:Name="btnSubmit" Text="Výber EAN" Image="ikona_vybrat50x50" HorizontalOptions="CenterAndExpand" Command="{Binding VyberCommand}" Margin="0,-50,0,0" WidthRequest="300" ></Button> -->
                </StackLayout>
            </StackLayout>
            <Button x:Name="btnVyber" 
                    Text="{i18n:Translate vyber}" 
                    ImageSource="ikona_vybrat50x50" 
                    Command="{Binding VyberCommand}" 
                    Margin="0,20,0,0" 
                    HorizontalOptions="FillAndExpand"
                    IsEnabled = "false"                    
                    >
            </Button>
            
        </RelativeLayout>
    </RelativeLayout>
</ContentPage>