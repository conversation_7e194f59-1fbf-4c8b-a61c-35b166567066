﻿using Mraznicka.Models;
using Mraznicka.ViewModels;
using Mraznicka.ViewModels.Vyber;
using Plugin.SimpleAudioPlayer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using Xamarin.Essentials;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using ZXing;

namespace Mraznicka.Views.Vyber
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class CompareEANPage : ContentPage
	{
		bool bMozemSkenovat = true;
        System.Timers.Timer _timer;
		string stary_result = "";
        private CompareEANPageViewModel ctx { get; set; }
		public CompareEANPage()
		{
			InitializeComponent();
			this.BindingContext = this.ctx =  new ViewModels.Vyber.CompareEANPageViewModel(this);
		}

        private void StartTimer()
        {
            _timer = new System.Timers.Timer(4000); // Interval v milisekundách (napr. 1000ms = 1 sekunda)
            _timer.Elapsed += OnTimedEvent;
            _timer.AutoReset = true; // Nastaví časovač na opakované spúšťanie
            _timer.Enabled = true; // Aktivuje časovač
        }

        private void StopTimer()
        {
            _timer.Stop();
            _timer.Dispose();
        }

        private void OnTimedEvent(object sender, ElapsedEventArgs e)
        {
            bMozemSkenovat = true;
            StopTimer();
        }


        public void Handle_OnScanResult(Result result)
		{
            if (!bMozemSkenovat)
                return;
            bMozemSkenovat = false;
            StartTimer();

            Device.BeginInvokeOnMainThread(async () =>
			{
                var duration = TimeSpan.FromSeconds(1);
				Vibration.Vibrate(duration);
				/*
                Mraznicka.Models.Zvuk zvuk = new Zvuk();
                zvuk.Speech(Mraznicka.Resources.AppResources.zvuk_dobre);
				*/

				//DisplayAlert("Scanned result", result.Text, "OK");
				if (stary_result != result.Text)
				{
					stary_result = result.Text;
                    //bMozemSkenovat = true;
                }
                this.ctx.Compare(result.Text);

			});
			
		}

		protected override void OnAppearing()
		{
			base.OnAppearing();
			ctx.OnAppearing();
			//_scanView.IsScanning = true;
		}

		protected override void OnDisappearing()
		{
			base.OnDisappearing();

			//_scanView.IsScanning = false;
		}
	}
}