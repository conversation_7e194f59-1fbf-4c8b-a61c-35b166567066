﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             x:Class="Mraznicka.Views.PullPage"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate vyber}">

	<RelativeLayout>
		<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>

		<RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
			<!--<Image Source="istockphoto1.jpg" Opacity="1" Aspect="AspectFit" VerticalOptions="EndAndExpand"></Image>-->
            <Label Margin="20" TextColor="#666" WidthRequest="230" LineBreakMode="WordWrap" Text="{i18n:Translate popisvyber}"></Label>
		</RelativeLayout>

		<ScrollView Style="{StaticResource MainSroll}" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=1}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=1}">
			<StackLayout>
                <Button Image="ikona_tag50x50.png" Text="{i18n:Translate tag}" Command="{Binding TagCommand}" WidthRequest="100" HeightRequest="70"  Margin="0" />
                <Button Image="ikona_ean50x50.png" Text="{i18n:Translate ean}" Command="{Binding EanCommand}"  WidthRequest="100"  HeightRequest="70" Margin="0"/>
                <Button Image="ikona_ruka50x50.png" Text="{i18n:Translate manual}" Command="{Binding ManualCommand}"  WidthRequest="100"  HeightRequest="70" Margin="0"/>
			</StackLayout>
		</ScrollView>

		<RelativeLayout RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-60}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
			<Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFill" ></Image>
		</RelativeLayout>
	</RelativeLayout>
</ContentPage>