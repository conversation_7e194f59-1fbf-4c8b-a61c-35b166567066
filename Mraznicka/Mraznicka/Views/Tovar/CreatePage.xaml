﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Mraznicka.Views.Tovar.CreatePage"
			 BackgroundColor="#F2F7FA"
			 xmlns:i18n="clr-namespace:Mraznicka.Helpers" 
			 Title="{i18n:Translate novytovar}">
    
    
	<RelativeLayout>
		<!--<Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="page_background.png" Aspect="AspectFill"/>

		<RelativeLayout RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}">
			<Label Style="{StaticResource Note}" Text="{i18n:Translate popistovarvytvorenie}"></Label>
		</RelativeLayout>-->

		<StackLayout Margin="0,0,0,80" Padding="20" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width}" RelativeLayout.HeightConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height}">
			<StackLayout>
				<Label Text="{i18n:Translate nazov}"  Style="{DynamicResource KeyLabel}" />
				<Entry  Text="{Binding Item.Nazov, Mode=TwoWay}" Style="{StaticResource ValueEntry}" Placeholder="{i18n:Translate zadajtenazov}" />
			</StackLayout>
			<StackLayout>
				<Label Text="{i18n:Translate expiraciadni}"  Style="{DynamicResource KeyLabel}" />
				<Entry Text="{Binding Item.Expiracia, Mode=TwoWay}" Keyboard="Numeric" Style="{StaticResource ValueEntry}" Placeholder="{i18n:Translate zadajteexpiraciu}" />
			</StackLayout>
		</StackLayout>
        <RelativeLayout  RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,Property=Height,Constant=-110}" RelativeLayout.WidthConstraint="{ConstraintExpression Type=RelativeToParent,Property=Width,Factor=1}" HorizontalOptions="Center">
            <StackLayout>
                <StackLayout Orientation="Horizontal" >
                    <Button Text="{i18n:Translate ulozit}" Image="ikona_ano_ulozit50x50.png" ContentLayout="Left,20" Command="{Binding SaveCommand}" HorizontalOptions="FillAndExpand" Margin="0"></Button>
                </StackLayout>
                <Image Source="logo.png" Opacity="0.8" HeightRequest="45" Aspect="AspectFit" ></Image>
            </StackLayout>
        </RelativeLayout>
    </RelativeLayout>
	

</ContentPage>
