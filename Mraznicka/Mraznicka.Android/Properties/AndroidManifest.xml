﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:versionCode="2" android:versionName="1.5.8" package="mraznicka_1_5_8.mraznicka_1_5_8" android:installLocation="auto">
	<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="31" />
	<application android:label="Mraznicka.Android" android:theme="@style/MainTheme" android:icon="@mipmap/icon"></application>
	<!--receiver android:name=".BackgroundReceiver"></receiver-->
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.NFC" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.GET_TASKS" />
	<uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
	<uses-permission android:name="android.permission.CAMERA" />
	<!--<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.Manifest.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />-->
</manifest>