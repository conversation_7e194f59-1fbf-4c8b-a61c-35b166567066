﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{627A2D3C-7BFB-490F-AEA9-6D18F22D6166}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{c9e5eea5-ca05-42a1-839b-61506e0a37df}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Mraznicka.Droid</RootNamespace>
    <AssemblyName>Mraznicka.Android</AssemblyName>
    <Deterministic>True</Deterministic>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <AndroidUseLatestPlatformSdk>false</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v12.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <AndroidKeyStore>false</AndroidKeyStore>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidCreatePackagePerAbi>true</AndroidCreatePackagePerAbi>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
    <MandroidI18n />
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Plugin.NFC">
      <Version>0.1.22</Version>
    </PackageReference>
    <PackageReference Include="Plugin.Toast">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.3" />
    <PackageReference Include="Xamarin.Google.ZXing.Core">
      <Version>3.3.3</Version>
    </PackageReference>
    <PackageReference Include="ZXing.Net.Mobile">
      <Version>2.4.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AlarmReceiver.cs" />
    <Compile Include="AndroidMethods.cs" />
    <Compile Include="BackgroundReceiver.cs" />
    <Compile Include="BackgroundService.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="PeriodicBackgroundService.cs" />
    <Compile Include="PeriodicService.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SplashScreenActivity.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\mipmap-hdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mraznicka\Mraznicka.csproj">
      <Project>{573EB54D-807B-4B24-8744-F13E4A58B783}</Project>
      <Name>Mraznicka</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\xamarin_logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_about.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_feed.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\splash_screen.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\splash_logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_push.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_pull.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_position.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_item.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_device.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_locality.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_comodity.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\mipmap-anydpi-v26\" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\nfc.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\page_background.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_bravcove.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_bylinky.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_hocico.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_hovadzie.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_hydina.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_kuracie.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_ovocie.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_ryby.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_zelenina.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_zverina.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_cukrovinky.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_hotovejedlo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_masovevyrobky.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_mliecnevyrobky.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_pecivo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikony_polotovar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_ean.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_prehlad.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_ruka.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_tag.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_vlozit.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_vybrat.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_tag50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_ean50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_prehlad50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_ruka50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_vlozit50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_vybrat50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_ano_ulozit50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_nie_zmazat50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_spat_cancel50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ikona_pridat50x50.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-cs\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-cs\splash_logo.png">
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-sk\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-sk\splash_logo.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties XamarinHotReloadDebuggerTimeoutExceptionMraznickaAndroidHideInfoBar="True" />
    </VisualStudio>
  </ProjectExtensions>
</Project>