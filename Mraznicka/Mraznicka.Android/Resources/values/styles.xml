<?xml version="1.0" encoding="utf-8" ?>
<resources>

  <style name="MainTheme" parent="MainTheme.Base">
    <!-- As of Xamarin.Forms 4.6 the theme has moved into the Forms binary -->
    <!-- If you want to override anything you can do that here. -->
    <!-- Underneath are a couple of entries to get you started. -->

    <!-- Set theme colors from https://aka.ms/material-colors -->
    <!-- colorPrimary is used for the default action bar background -->
    <!--<item name="colorPrimary">#2196F3</item>-->
    <!-- colorPrimaryDark is used for the status bar -->
    <!--<item name="colorPrimaryDark">#1976D2</item>-->
    <!-- colorAccent is used as the default value for colorControlActivated
         which is used to tint widgets -->
    <!--<item name="colorAccent">#FF4081</item>-->
  </style>

	<style name="QSoft.Splash" parent ="Theme.AppCompat.Light.NoActionBar">
		<item name="android:windowBackground">@drawable/splash_screen</item>
		<item name="android:windowNoTitle">true</item>
		<item name="android:windowFullscreen">true</item>
		<item name="android:windowContentOverlay">@null</item>
		<item name="android:windowActionBar">true</item>
	</style>
</resources>